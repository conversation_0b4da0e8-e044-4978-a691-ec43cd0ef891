import { NextRequest, NextResponse } from 'next/server'
import { existsSync } from 'fs'
import path from 'path'

// GET - 检查安装状态
export async function GET(request: NextRequest) {
  try {
    // 检查是否存在安装完成标记文件
    const installLockFile = path.join(process.cwd(), '.install.lock')
    const envFile = path.join(process.cwd(), '.env.local')
    
    const installed = existsSync(installLockFile) && existsSync(envFile)
    
    return NextResponse.json({
      success: true,
      installed,
      message: installed ? '项目已安装' : '项目未安装'
    })
    
  } catch (error) {
    console.error('检查安装状态失败:', error)
    return NextResponse.json({
      success: false,
      installed: false,
      error: '检查安装状态失败'
    }, { status: 500 })
  }
}
