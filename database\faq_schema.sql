-- 常见问题数据库表设计
USE dianfuto;

-- 创建常见问题表
CREATE TABLE IF NOT EXISTS faqs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    question VARCHAR(500) NOT NULL COMMENT '问题标题',
    answer TEXT NOT NULL COMMENT '问题答案',
    category VARCHAR(100) DEFAULT 'general' COMMENT '问题分类',
    sort_order INT DEFAULT 0 COMMENT '排序顺序，数字越小越靠前',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用显示',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    created_by VARCHAR(100) DEFAULT 'admin' COMMENT '创建者',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='常见问题表';

-- 插入默认的常见问题数据
INSERT INTO faqs (question, answer, category, sort_order, is_active) VALUES
('什么是滇护通医疗陪护服务？', 
'滇护通是一家专业的医疗陪护服务平台，为用户提供陪诊、医疗跑腿、代办服务、上门护理等全方位的医疗陪护服务。我们拥有专业的陪护团队，致力于让医疗陪护更简单、更安心。', 
'service', 1, TRUE),

('如何预约陪护服务？', 
'您可以通过以下方式预约我们的服务：1. 在线预约：通过我们的官方网站或微信小程序提交预约申请；2. 电话预约：拨打我们的服务热线400-123-4567；3. 微信预约：关注我们的官方微信公众号进行预约。我们的客服人员会在收到预约后及时与您联系确认服务详情。', 
'booking', 2, TRUE),

('陪护服务的收费标准是什么？', 
'我们的收费标准根据服务类型和时长而定：1. 陪诊服务：按小时收费，包含交通费用；2. 医疗跑腿：按次收费，根据距离和复杂程度定价；3. 上门护理：按服务项目收费；4. 代办服务：按事项收费。具体价格请咨询客服，我们会根据您的具体需求提供详细报价。', 
'pricing', 3, TRUE),

('陪护人员是否具备专业资质？', 
'是的，我们所有的陪护人员都具备相应的专业资质：1. 持有护理相关证书或培训合格证；2. 具有丰富的医疗陪护经验；3. 通过我们严格的背景调查和技能考核；4. 定期接受专业培训和考核。我们确保每位陪护人员都能提供专业、安全、贴心的服务。', 
'staff', 4, TRUE),

('服务覆盖哪些地区？', 
'目前我们的服务主要覆盖云南省内的主要城市，包括昆明、大理、丽江、曲靖、玉溪等地区。我们正在不断扩大服务范围，计划在更多城市开展业务。如果您所在的地区暂未覆盖，请联系我们的客服，我们会尽力为您提供解决方案。', 
'coverage', 5, TRUE),

('如果对服务不满意怎么办？', 
'我们非常重视客户的服务体验，如果您对我们的服务不满意：1. 请及时联系我们的客服热线反馈问题；2. 我们会立即调查并采取改进措施；3. 根据情况提供服务补偿或退款；4. 我们承诺7天内给出满意的解决方案。您的满意是我们不断改进的动力。', 
'complaint', 6, TRUE),

('紧急情况下如何联系你们？', 
'遇到紧急情况，请立即联系我们：1. 24小时紧急热线：400-123-4567；2. 微信客服：搜索"滇护通客服"；3. 在线客服：通过官网在线客服系统。我们的客服团队24小时待命，确保在紧急情况下能够及时响应并提供帮助。', 
'emergency', 7, TRUE),

('是否提供长期陪护服务？', 
'是的，我们提供长期陪护服务：1. 住院期间的全程陪护；2. 居家康复期间的定期护理；3. 慢性病患者的长期照护；4. 老年人的日常生活陪护。长期服务客户可享受优惠价格和专属服务团队。具体方案请联系我们的客服进行详细咨询。', 
'longterm', 8, TRUE);

-- 创建FAQ分类表（可选，用于更好的分类管理）
CREATE TABLE IF NOT EXISTS faq_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_key VARCHAR(100) NOT NULL UNIQUE COMMENT '分类键名',
    category_name VARCHAR(100) NOT NULL COMMENT '分类显示名称',
    description TEXT COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='FAQ分类表';

-- 插入默认分类
INSERT INTO faq_categories (category_key, category_name, description, sort_order) VALUES
('service', '服务介绍', '关于我们服务内容的问题', 1),
('booking', '预约流程', '关于如何预约服务的问题', 2),
('pricing', '收费标准', '关于服务价格和收费的问题', 3),
('staff', '人员资质', '关于陪护人员专业性的问题', 4),
('coverage', '服务范围', '关于服务覆盖地区的问题', 5),
('complaint', '售后服务', '关于服务质量和投诉的问题', 6),
('emergency', '紧急联系', '关于紧急情况处理的问题', 7),
('longterm', '长期服务', '关于长期陪护服务的问题', 8);
