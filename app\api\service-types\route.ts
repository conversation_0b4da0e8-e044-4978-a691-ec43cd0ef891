import { NextRequest, NextResponse } from 'next/server'
import { executeQuery, executeInsert, executeUpdate, executeDelete } from '@/lib/database'
import { ApiResponse } from '@/lib/types/database'

// 服务类型数据类型
export interface ServiceType {
  id: number
  service_name: string
  service_code: string
  description: string
  icon: string
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface ServiceTypeFormData {
  service_name: string
  service_code: string
  description?: string
  icon?: string
  sort_order?: number
  is_active?: boolean
}

// 模拟服务类型数据
const mockServiceTypes: ServiceType[] = [
  {
    id: 1,
    service_name: '陪诊服务',
    service_code: 'accompany_medical',
    description: '专业陪护人员陪同就医，提供全程陪诊服务',
    icon: '🏥',
    sort_order: 1,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 2,
    service_name: '医疗跑腿',
    service_code: 'medical_errand',
    description: '代办医疗相关事务，如取药、送检查报告等',
    icon: '🏃‍♂️',
    sort_order: 2,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 3,
    service_name: '上门护理',
    service_code: 'home_nursing',
    description: '专业护理人员提供上门护理服务',
    icon: '👩‍⚕️',
    sort_order: 3,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 4,
    service_name: '代办服务',
    service_code: 'proxy_service',
    description: '代办各类医疗手续和证明文件',
    icon: '📋',
    sort_order: 4,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 5,
    service_name: '健康咨询',
    service_code: 'health_consultation',
    description: '提供专业的健康咨询和建议',
    icon: '💬',
    sort_order: 5,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 6,
    service_name: '康复指导',
    service_code: 'rehabilitation_guidance',
    description: '专业康复师提供康复训练指导',
    icon: '🤸‍♀️',
    sort_order: 6,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 7,
    service_name: '心理疏导',
    service_code: 'psychological_counseling',
    description: '专业心理咨询师提供心理疏导服务',
    icon: '🧠',
    sort_order: 7,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 8,
    service_name: '营养配餐',
    service_code: 'nutrition_meal',
    description: '根据病情提供专业营养配餐服务',
    icon: '🍽️',
    sort_order: 8,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
]

// GET - 获取服务类型列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const active_only = searchParams.get('active_only') === 'true'

    let serviceTypes = mockServiceTypes
    if (active_only) {
      serviceTypes = mockServiceTypes.filter(type => type.is_active)
    }

    const response: ApiResponse<ServiceType[]> = {
      success: true,
      data: serviceTypes
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('获取服务类型失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '获取服务类型失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// POST - 创建新的服务类型
export async function POST(request: NextRequest) {
  try {
    const body: ServiceTypeFormData = await request.json()
    const { service_name, service_code, description, icon, sort_order, is_active } = body
    
    // 验证必需字段
    if (!service_name || !service_code) {
      const response: ApiResponse = {
        success: false,
        error: '服务名称和服务代码为必填项'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 检查服务代码是否已存在
    const existing = await executeQuery<ServiceType>(
      'SELECT * FROM service_types WHERE service_code = ?',
      [service_code]
    )
    
    if (existing.length > 0) {
      const response: ApiResponse = {
        success: false,
        error: '服务代码已存在'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const result = await executeInsert(
      `INSERT INTO service_types (service_name, service_code, description, icon, sort_order, is_active)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [service_name, service_code, description || '', icon || '', sort_order || 0, is_active !== false]
    )
    
    const response: ApiResponse = {
      success: true,
      message: '服务类型创建成功',
      data: { id: result.insertId }
    }
    return NextResponse.json(response)
  } catch (error) {
    console.error('创建服务类型失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '创建服务类型失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// PUT - 更新服务类型
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, service_name, service_code, description, icon, sort_order, is_active } = body
    
    // 验证必需字段
    if (!id || !service_name || !service_code) {
      const response: ApiResponse = {
        success: false,
        error: 'ID、服务名称和服务代码为必填项'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 检查服务代码是否被其他记录使用
    const existing = await executeQuery<ServiceType>(
      'SELECT * FROM service_types WHERE service_code = ? AND id != ?',
      [service_code, id]
    )
    
    if (existing.length > 0) {
      const response: ApiResponse = {
        success: false,
        error: '服务代码已被其他服务类型使用'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    await executeUpdate(
      `UPDATE service_types 
       SET service_name = ?, service_code = ?, description = ?, icon = ?, sort_order = ?, is_active = ?
       WHERE id = ?`,
      [service_name, service_code, description || '', icon || '', sort_order || 0, is_active !== false, id]
    )
    
    const response: ApiResponse = {
      success: true,
      message: '服务类型更新成功'
    }
    return NextResponse.json(response)
  } catch (error) {
    console.error('更新服务类型失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '更新服务类型失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// DELETE - 删除服务类型
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      const response: ApiResponse = {
        success: false,
        error: '服务类型ID为必填项'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 检查是否有地州在使用该服务类型
    const usageCheck = await executeQuery(
      'SELECT COUNT(*) as count FROM prefecture_services WHERE service_type_id = ?',
      [id]
    )
    
    if (usageCheck[0].count > 0) {
      const response: ApiResponse = {
        success: false,
        error: '该服务类型正在被使用，无法删除'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    await executeDelete('DELETE FROM service_types WHERE id = ?', [id])
    
    const response: ApiResponse = {
      success: true,
      message: '服务类型删除成功'
    }
    return NextResponse.json(response)
  } catch (error) {
    console.error('删除服务类型失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '删除服务类型失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
