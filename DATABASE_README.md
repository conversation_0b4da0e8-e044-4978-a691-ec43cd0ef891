# 滇护通数据库集成文档

## 概述

本项目已成功集成MySQL数据库，实现了完整的数据管理功能。包括网站设置管理、合作申请管理等核心功能。

## 数据库结构

### 1. 网站设置表 (site_settings)
存储网站的各种配置信息，支持动态修改网站内容。

**字段说明：**
- `id`: 主键
- `setting_key`: 设置键名（如：site_name, hero_title等）
- `setting_value`: 设置值
- `setting_type`: 设置类型（text, textarea, image, json）
- `description`: 设置描述
- `created_at`, `updated_at`: 时间戳

### 2. 合作申请表 (cooperation_applications)
存储用户提交的合作意向申请。

**字段说明：**
- `id`: 主键
- `name`: 申请人姓名
- `company`: 公司名称
- `phone`: 联系电话
- `email`: 邮箱地址
- `region`: 合作区域
- `cooperation_type`: 合作类型
- `experience`: 相关经验
- `message`: 合作意向说明
- `status`: 处理状态（pending, contacted, completed, rejected）
- `admin_notes`: 管理员备注
- `ip_address`: 提交IP地址
- `user_agent`: 用户代理
- `created_at`, `updated_at`: 时间戳

### 3. 图片管理表 (images)
管理网站使用的图片资源。

### 4. 管理员表 (admins)
管理后台登录用户信息。

## API接口

### 网站设置API

#### GET /api/settings
获取所有网站设置
```json
{
  "success": true,
  "data": {
    "site_name": "滇护通",
    "hero_title": "让医疗陪护更简单、更安心",
    ...
  }
}
```

#### POST /api/settings
更新所有网站设置
```json
{
  "site_name": "滇护通",
  "hero_title": "新标题",
  "hero_subtitle": "新副标题",
  ...
}
```

#### PUT /api/settings
更新单个设置项
```json
{
  "setting_key": "site_name",
  "setting_value": "新网站名称"
}
```

### 合作申请API

#### GET /api/cooperation
获取合作申请列表
- 支持分页：`?page=1&limit=10`
- 支持状态筛选：`?status=pending`

#### POST /api/cooperation
创建新的合作申请
```json
{
  "name": "张三",
  "company": "测试公司",
  "phone": "13800000000",
  "email": "<EMAIL>",
  "region": "昆明市",
  "cooperation_type": "区域代理",
  "experience": "有相关经验",
  "message": "合作意向说明"
}
```

#### PUT /api/cooperation/[id]
更新合作申请状态
```json
{
  "status": "contacted",
  "admin_notes": "已联系用户"
}
```

#### DELETE /api/cooperation/[id]
删除合作申请

## 功能特性

### ✅ 已实现功能

1. **网站设置管理**
   - 动态修改网站标题、副标题
   - 管理联系信息
   - 更新关于我们内容
   - 修改版权和备案信息

2. **合作申请管理**
   - 用户提交合作申请
   - 管理员查看申请列表
   - 更新申请处理状态
   - 添加管理员备注

3. **数据安全**
   - 参数验证
   - SQL注入防护
   - 错误处理
   - 连接池管理

4. **用户体验**
   - 加载状态显示
   - 错误提示
   - 成功反馈
   - 响应式设计

## 环境配置

### 数据库配置 (.env.local)
```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=dianfuto
DATABASE_URL=mysql://root:password@localhost:3306/dianfuto
```

### 依赖包
- `mysql2`: MySQL数据库驱动
- `@types/mysql2`: TypeScript类型定义

## 使用说明

### 1. 启动开发服务器
```bash
yarn dev
```

### 2. 访问管理后台
访问 `http://localhost:3000/admin` 进入管理后台

### 3. 测试合作申请
访问 `http://localhost:3000/cooperation` 提交合作申请

### 4. 运行测试
```bash
# 测试数据库连接
node scripts/test-db.js

# 测试API功能
node scripts/test-api.js
```

## 开发指南

### 添加新的设置项
1. 在数据库中插入新的设置记录
2. 更新 `SiteSettingsFormData` 类型定义
3. 在管理后台添加对应的表单字段

### 扩展合作申请功能
1. 修改数据库表结构
2. 更新 `CooperationApplication` 类型定义
3. 修改API接口和前端表单

## 测试结果

✅ 数据库连接测试通过
✅ 网站设置API测试通过
✅ 合作申请API测试通过
✅ 前端集成测试通过
✅ 数据持久化验证通过

## 技术栈

- **前端**: Next.js 15 + React 19 + TypeScript
- **数据库**: MySQL 5.7
- **ORM**: 原生SQL查询（使用mysql2）
- **样式**: Tailwind CSS + shadcn/ui

## 注意事项

1. 确保MySQL服务正在运行
2. 检查数据库连接配置
3. 定期备份数据库
4. 监控API性能和错误日志
