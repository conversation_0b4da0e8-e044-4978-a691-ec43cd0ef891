import { NextRequest, NextResponse } from 'next/server'
import { executeQuery, executeInsert, executeUpdate } from '@/lib/database'
import { CooperationApplication, ApiResponse, CooperationFormData, PaginatedResponse } from '@/lib/types/database'

// GET - 获取合作申请列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const offset = (page - 1) * limit
    
    // 构建查询条件
    let whereClause = ''
    let queryParams: any[] = []
    
    if (status && status !== 'all') {
      whereClause = 'WHERE status = ?'
      queryParams.push(status)
    }
    
    // 获取总数
    const totalQuery = `SELECT COUNT(*) as total FROM cooperation_applications ${whereClause}`
    const totalResult = await executeQuery<{ total: number }>(totalQuery, queryParams)
    const total = totalResult[0].total
    
    // 获取数据
    const dataQuery = `
      SELECT * FROM cooperation_applications 
      ${whereClause}
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `
    const applications = await executeQuery<CooperationApplication>(
      dataQuery, 
      [...queryParams, limit, offset]
    )
    
    const response: ApiResponse<PaginatedResponse<CooperationApplication>> = {
      success: true,
      data: {
        data: applications,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('获取合作申请失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '获取合作申请失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// POST - 创建新的合作申请
export async function POST(request: NextRequest) {
  try {
    const body: CooperationFormData = await request.json()
    
    // 验证必填字段
    if (!body.name || !body.phone || !body.region || !body.cooperation_type || !body.message) {
      const response: ApiResponse = {
        success: false,
        error: '缺少必填字段'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 获取客户端信息
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'
    
    // 插入数据
    const insertId = await executeInsert(
      `INSERT INTO cooperation_applications 
       (name, company, phone, email, region, cooperation_type, experience, message, ip_address, user_agent) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        body.name,
        body.company || null,
        body.phone,
        body.email || null,
        body.region,
        body.cooperation_type,
        body.experience || null,
        body.message,
        ip,
        userAgent
      ]
    )
    
    const response: ApiResponse<{ id: number }> = {
      success: true,
      data: { id: insertId },
      message: '合作申请提交成功'
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('创建合作申请失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '提交合作申请失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
