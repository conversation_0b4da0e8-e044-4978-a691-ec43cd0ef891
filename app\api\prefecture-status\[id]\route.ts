import { NextRequest, NextResponse } from 'next/server'
import { executeQuery, executeUpdate } from '@/lib/database'
import { PrefectureStatus, ApiResponse, PrefectureStatusFormData, ServiceStatus } from '@/lib/types/database'

// 获取状态对应的颜色代码
const getStatusColor = (status: ServiceStatus): string => {
  switch (status) {
    case '未开通':
      return '#CCCCCC'
    case '部分开通':
      return '#FFA500'
    case '已开通':
      return '#10B981'
    default:
      return '#CCCCCC'
  }
}

// GET - 获取单个地州状态详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      const response: ApiResponse = {
        success: false,
        error: '无效的ID'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const prefecture = await executeQuery<PrefectureStatus>(
      'SELECT * FROM prefecture_status WHERE id = ?',
      [id]
    )
    
    if (prefecture.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: '地州不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse<PrefectureStatus> = {
      success: true,
      data: prefecture[0]
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('获取地州状态详情失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '获取地州状态详情失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// PUT - 更新地州状态
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    const body: PrefectureStatusFormData = await request.json()
    
    if (isNaN(id)) {
      const response: ApiResponse = {
        success: false,
        error: '无效的ID'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    if (!body.prefecture_name || !body.prefecture_code || !body.service_status) {
      const response: ApiResponse = {
        success: false,
        error: '地州名称、代码和服务状态为必填字段'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 检查代码是否与其他地州冲突
    const existing = await executeQuery<PrefectureStatus>(
      'SELECT id FROM prefecture_status WHERE prefecture_code = ? AND id != ?',
      [body.prefecture_code, id]
    )
    
    if (existing.length > 0) {
      const response: ApiResponse = {
        success: false,
        error: '地州代码已被其他地州使用'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 自动设置颜色代码
    const colorCode = body.color_code || getStatusColor(body.service_status)
    
    // 更新地州状态
    const affectedRows = await executeUpdate(
      `UPDATE prefecture_status SET 
       prefecture_name = ?, prefecture_code = ?, service_status = ?, 
       color_code = ?, description = ?, sort_order = ?, is_active = ?, 
       updated_at = NOW() 
       WHERE id = ?`,
      [
        body.prefecture_name,
        body.prefecture_code,
        body.service_status,
        colorCode,
        body.description || '',
        body.sort_order || 0,
        body.is_active !== undefined ? body.is_active : true,
        id
      ]
    )
    
    if (affectedRows === 0) {
      const response: ApiResponse = {
        success: false,
        error: '地州不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse = {
      success: true,
      message: '地州状态更新成功'
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('更新地州状态失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '更新地州状态失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// DELETE - 删除地州状态
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      const response: ApiResponse = {
        success: false,
        error: '无效的ID'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const affectedRows = await executeUpdate(
      'DELETE FROM prefecture_status WHERE id = ?',
      [id]
    )
    
    if (affectedRows === 0) {
      const response: ApiResponse = {
        success: false,
        error: '地州不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse = {
      success: true,
      message: '地州状态删除成功'
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('删除地州状态失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '删除地州状态失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
