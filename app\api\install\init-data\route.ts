import { NextRequest, NextResponse } from 'next/server'
import mysql from 'mysql2/promise'
import bcrypt from 'bcryptjs'

// POST - 初始化数据
export async function POST(request: NextRequest) {
  let connection: mysql.Connection | null = null
  
  try {
    const { admin, site } = await request.json()
    
    // 从环境变量读取数据库配置
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'dianfuto'
    }
    
    // 连接数据库
    connection = await mysql.createConnection(dbConfig)

    // 1. 插入网站设置
    const settings = [
      ['site_name', site.siteName || '滇护通', '网站名称'],
      ['hero_title', '让医疗陪护更简单、更安心', '首页标题'],
      ['hero_subtitle', '足不出户，为家人预约专业的陪诊、医疗跑腿、代办、上门护工等服务', '首页副标题'],
      ['hero_image', '', '首页头图'],
      ['site_logo', '', '网站Logo'],
      ['miniprogram_qrcode', '', '小程序码图片路径'],
      ['about_us', '滇护通是一家专业的医疗陪护服务平台，致力于为用户提供便捷、可靠的医疗陪护服务。我们拥有专业的陪护团队和完善的服务体系，让您的家人享受到最贴心的医疗陪护服务。', '关于我们'],
      ['copyright', '2024 滇护通医疗陪护服务平台. 保留所有权利.', '版权信息'],
      ['icp', '滇ICP备2024000001号', '备案号'],
      ['phone', '************', '联系电话'],
      ['address', '云南省昆明市五华区春城路100号', '公司地址'],
      ['email', site.adminEmail || '<EMAIL>', '联系邮箱']
    ]

    for (const [key, value, description] of settings) {
      await connection.execute(
        'INSERT INTO settings (setting_key, setting_value, description) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), description = VALUES(description)',
        [key, value, description]
      )
    }

    // 2. 创建管理员账户
    const hashedPassword = await bcrypt.hash(admin.password, 12)
    await connection.execute(
      'INSERT INTO admins (username, password, email, full_name, is_active) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE password = VALUES(password), email = VALUES(email)',
      [admin.username, hashedPassword, admin.email, '系统管理员', true]
    )

    // 3. 插入默认FAQ分类
    const faqCategories = [
      ['服务相关', '关于医疗陪护服务的常见问题', 1],
      ['预约流程', '关于预约流程的常见问题', 2],
      ['费用说明', '关于服务费用的常见问题', 3],
      ['其他问题', '其他常见问题', 4]
    ]

    for (const [name, description, sortOrder] of faqCategories) {
      await connection.execute(
        'INSERT INTO faq_categories (name, description, sort_order, is_active) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE description = VALUES(description)',
        [name, description, sortOrder, true]
      )
    }

    // 4. 插入默认FAQ
    const faqs = [
      ['什么是医疗陪护服务？', '医疗陪护服务是指专业人员陪同患者就医，提供挂号、排队、取药、检查等全程陪护服务，让患者和家属更加安心便捷。', '服务相关', 1],
      ['如何预约陪护服务？', '您可以通过我们的小程序或网站在线预约，选择服务类型、时间和地点，我们会安排专业的陪护人员为您服务。', '预约流程', 2],
      ['陪护服务的费用如何计算？', '费用根据服务类型、时长和地区有所不同，具体价格请在预约时查看，我们提供透明的价格体系。', '费用说明', 3],
      ['陪护人员是否专业？', '我们的陪护人员都经过专业培训，具备医疗基础知识和丰富的陪护经验，能够为您提供专业、贴心的服务。', '服务相关', 4],
      ['可以取消或修改预约吗？', '可以的，您可以在服务开始前2小时通过小程序或联系客服取消或修改预约，具体政策请查看预约须知。', '预约流程', 5]
    ]

    for (const [question, answer, category, sortOrder] of faqs) {
      await connection.execute(
        'INSERT INTO faqs (question, answer, category, sort_order, is_active) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE answer = VALUES(answer)',
        [question, answer, category, sortOrder, true]
      )
    }

    // 5. 插入云南省地州服务状态
    const prefectures = [
      ['昆明市', 'available', '服务已开通，提供全面的医疗陪护服务', '************', '<EMAIL>', 1],
      ['曲靖市', 'coming_soon', '即将开通服务，敬请期待', '', '', 2],
      ['玉溪市', 'coming_soon', '即将开通服务，敬请期待', '', '', 3],
      ['保山市', 'coming_soon', '即将开通服务，敬请期待', '', '', 4],
      ['昭通市', 'coming_soon', '即将开通服务，敬请期待', '', '', 5],
      ['丽江市', 'coming_soon', '即将开通服务，敬请期待', '', '', 6],
      ['普洱市', 'coming_soon', '即将开通服务，敬请期待', '', '', 7],
      ['临沧市', 'coming_soon', '即将开通服务，敬请期待', '', '', 8],
      ['楚雄彝族自治州', 'coming_soon', '即将开通服务，敬请期待', '', '', 9],
      ['红河哈尼族彝族自治州', 'coming_soon', '即将开通服务，敬请期待', '', '', 10],
      ['文山壮族苗族自治州', 'coming_soon', '即将开通服务，敬请期待', '', '', 11],
      ['西双版纳傣族自治州', 'coming_soon', '即将开通服务，敬请期待', '', '', 12],
      ['大理白族自治州', 'coming_soon', '即将开通服务，敬请期待', '', '', 13],
      ['德宏傣族景颇族自治州', 'coming_soon', '即将开通服务，敬请期待', '', '', 14],
      ['怒江傈僳族自治州', 'coming_soon', '即将开通服务，敬请期待', '', '', 15],
      ['迪庆藏族自治州', 'coming_soon', '即将开通服务，敬请期待', '', '', 16]
    ]

    for (const [name, status, description, phone, email, sortOrder] of prefectures) {
      await connection.execute(
        'INSERT INTO prefecture_status (prefecture_name, service_status, description, contact_phone, contact_email, sort_order, is_active) VALUES (?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE service_status = VALUES(service_status), description = VALUES(description)',
        [name, status, description, phone, email, sortOrder, true]
      )
    }

    // 6. 记录安装日志
    await connection.execute(
      'INSERT INTO system_logs (level, message, context) VALUES (?, ?, ?)',
      ['info', '系统安装完成', JSON.stringify({
        admin_username: admin.username,
        site_name: site.siteName,
        install_time: new Date().toISOString()
      })]
    )

    return NextResponse.json({
      success: true,
      message: '初始数据创建成功',
      data: {
        settings: settings.length,
        admin: 1,
        faq_categories: faqCategories.length,
        faqs: faqs.length,
        prefectures: prefectures.length
      }
    })
    
  } catch (error: any) {
    console.error('初始化数据失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error.message || '初始化数据失败',
      code: error.code
    }, { status: 500 })
    
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}
