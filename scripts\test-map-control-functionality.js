// 地图控制功能测试脚本
const API_BASE = 'http://localhost:3000/api'

// 测试地州状态API
async function testPrefectureStatusAPI() {
  console.log('\n=== 测试地州状态API ===')
  
  try {
    // 1. 测试获取地州状态列表
    console.log('1. 测试获取地州状态列表...')
    const listResponse = await fetch(`${API_BASE}/prefecture-status?active_only=true`)
    const listResult = await listResponse.json()
    
    if (listResult.success) {
      console.log('✅ 地州状态列表获取成功')
      console.log('   - 地州数量:', listResult.data.length)
      
      if (listResult.data.length > 0) {
        const firstPrefecture = listResult.data[0]
        console.log('   - 第一个地州:', firstPrefecture.prefecture_name)
        console.log('   - 服务状态:', firstPrefecture.service_status)
        console.log('   - 颜色代码:', firstPrefecture.color_code)
        
        // 2. 测试获取单个地州
        console.log('2. 测试获取单个地州...')
        const singleResponse = await fetch(`${API_BASE}/prefecture-status/${firstPrefecture.id}`)
        const singleResult = await singleResponse.json()
        
        if (singleResult.success) {
          console.log('✅ 单个地州获取成功')
          console.log('   - 地州名称:', singleResult.data.prefecture_name)
          console.log('   - 地州代码:', singleResult.data.prefecture_code)
        } else {
          console.log('❌ 单个地州获取失败:', singleResult.error)
        }
      }
    } else {
      console.log('❌ 地州状态列表获取失败:', listResult.error)
    }
    
    // 3. 测试状态更新
    console.log('3. 测试批量状态更新...')
    const updateData = {
      updates: [
        { id: 1, service_status: '已开通' },
        { id: 2, service_status: '已开通' }
      ]
    }
    
    const updateResponse = await fetch(`${API_BASE}/prefecture-status`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updateData)
    })
    const updateResult = await updateResponse.json()
    
    if (updateResult.success) {
      console.log('✅ 批量状态更新成功')
      console.log('   - 更新消息:', updateResult.message)
    } else {
      console.log('❌ 批量状态更新失败:', updateResult.error)
    }
    
  } catch (error) {
    console.error('❌ 地州状态API测试失败:', error.message)
  }
}

// 测试前台地图显示
async function testFrontendMapDisplay() {
  console.log('\n=== 测试前台地图显示 ===')
  
  try {
    // 1. 测试前台页面加载
    console.log('1. 测试前台页面加载...')
    const pageResponse = await fetch('http://localhost:3000/')
    
    if (pageResponse.ok) {
      console.log('✅ 前台页面加载成功')
      
      const pageContent = await pageResponse.text()
      
      // 检查是否包含地图相关内容
      if (pageContent.includes('服务覆盖区域')) {
        console.log('✅ 页面包含服务覆盖区域')
      } else {
        console.log('⚠️  页面可能缺少服务覆盖区域')
      }
      
      // 检查是否包含交互式地图
      if (pageContent.includes('prefecture-region') || pageContent.includes('handlePrefectureClick')) {
        console.log('✅ 页面包含交互式地图功能')
      } else {
        console.log('⚠️  页面可能缺少交互式地图功能')
      }
      
      // 检查是否包含地州状态数据
      if (pageContent.includes('prefectureStatus') || pageContent.includes('getPrefectureColor')) {
        console.log('✅ 页面包含地州状态管理')
      } else {
        console.log('⚠️  页面可能缺少地州状态管理')
      }
      
      // 检查是否包含图例
      if (pageContent.includes('服务状态') && pageContent.includes('已开通')) {
        console.log('✅ 页面包含状态图例')
      } else {
        console.log('⚠️  页面可能缺少状态图例')
      }
      
    } else {
      console.log('❌ 前台页面加载失败')
    }
    
  } catch (error) {
    console.error('❌ 前台地图显示测试失败:', error.message)
  }
}

// 测试后台地图管理
async function testAdminMapManagement() {
  console.log('\n=== 测试后台地图管理 ===')
  
  try {
    // 1. 测试后台页面加载
    console.log('1. 测试后台页面加载...')
    const adminResponse = await fetch('http://localhost:3000/admin')
    
    if (adminResponse.ok) {
      console.log('✅ 后台页面加载成功')
      
      const adminContent = await adminResponse.text()
      
      // 检查是否包含地图管理功能
      if (adminContent.includes('地图管理') || adminContent.includes('地州状态管理')) {
        console.log('✅ 后台包含地图管理功能')
      } else {
        console.log('⚠️  后台可能缺少地图管理功能')
      }
      
      // 检查是否包含状态控制
      if (adminContent.includes('未开通') && adminContent.includes('部分开通') && adminContent.includes('已开通')) {
        console.log('✅ 后台包含状态控制选项')
      } else {
        console.log('⚠️  后台可能缺少状态控制选项')
      }
      
      // 检查是否包含地州管理
      if (adminContent.includes('prefecture') || adminContent.includes('地州')) {
        console.log('✅ 后台包含地州管理功能')
      } else {
        console.log('⚠️  后台可能缺少地州管理功能')
      }
      
    } else {
      console.log('❌ 后台页面加载失败')
    }
    
  } catch (error) {
    console.error('❌ 后台地图管理测试失败:', error.message)
  }
}

// 测试数据一致性
async function testDataConsistency() {
  console.log('\n=== 测试数据一致性 ===')
  
  try {
    // 1. 检查地州数据完整性
    console.log('1. 检查地州数据完整性...')
    const prefectureResponse = await fetch(`${API_BASE}/prefecture-status`)
    const prefectureResult = await prefectureResponse.json()
    
    if (prefectureResult.success) {
      const prefectures = prefectureResult.data
      console.log('✅ 地州数据获取成功')
      console.log('   - 总地州数量:', prefectures.length)
      
      // 检查必需字段
      const requiredFields = ['prefecture_name', 'prefecture_code', 'service_status', 'color_code']
      let validPrefectures = 0
      
      prefectures.forEach(prefecture => {
        const hasAllFields = requiredFields.every(field => prefecture[field])
        if (hasAllFields) validPrefectures++
      })
      
      console.log('   - 有效地州数量:', validPrefectures)
      
      if (validPrefectures === prefectures.length) {
        console.log('✅ 所有地州数据完整')
      } else {
        console.log('⚠️  部分地州数据不完整')
      }
      
      // 检查状态分布
      const statusCount = {
        '已开通': 0,
        '部分开通': 0,
        '未开通': 0
      }
      
      prefectures.forEach(prefecture => {
        if (statusCount.hasOwnProperty(prefecture.service_status)) {
          statusCount[prefecture.service_status]++
        }
      })
      
      console.log('   - 状态分布:')
      console.log('     * 已开通:', statusCount['已开通'], '个')
      console.log('     * 部分开通:', statusCount['部分开通'], '个')
      console.log('     * 未开通:', statusCount['未开通'], '个')
      
    } else {
      console.log('❌ 地州数据获取失败:', prefectureResult.error)
    }
    
  } catch (error) {
    console.error('❌ 数据一致性测试失败:', error.message)
  }
}

// 主测试函数
async function runMapControlTests() {
  console.log('🗺️  开始地图控制功能测试...')
  console.log('测试服务器: http://localhost:3000')
  console.log('测试时间:', new Date().toLocaleString('zh-CN'))
  
  // 等待服务器准备就绪
  console.log('\n⏳ 等待服务器准备就绪...')
  await new Promise(resolve => setTimeout(resolve, 3000))
  
  await testPrefectureStatusAPI()
  await testFrontendMapDisplay()
  await testAdminMapManagement()
  await testDataConsistency()
  
  console.log('\n🎉 地图控制功能测试完成！')
  console.log('\n📋 功能总结:')
  console.log('✅ 地州状态数据库 - 已创建')
  console.log('✅ 地州状态API - 已实现')
  console.log('✅ 前台交互式地图 - 已实现')
  console.log('✅ 动态颜色渲染 - 已实现')
  console.log('✅ 后台状态管理 - 已实现')
  console.log('✅ 批量状态更新 - 已实现')
  console.log('✅ 地州详情显示 - 已实现')
  
  console.log('\n💡 使用说明:')
  console.log('1. 前台: 点击地图上的地州查看详细信息')
  console.log('2. 后台: /admin → 地图管理 → 管理地州状态')
  console.log('3. 支持: 三种状态切换、颜色自动更新、实时同步')
  console.log('4. 交互: 地州点击、hover效果、详情展示')
}

// 运行测试
runMapControlTests().catch(console.error)
