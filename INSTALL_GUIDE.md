# 滇护通项目安装指南

## 🚀 Web安装程序

滇护通项目现已配备完整的Web安装程序，您可以通过浏览器界面轻松完成项目的安装和配置。

## 📋 安装前准备

### 系统要求
- **Node.js**: 版本 18.0 或更高
- **MySQL**: 版本 5.7 或更高（推荐 8.0+）
- **操作系统**: Windows、macOS、Linux
- **磁盘空间**: 至少 500MB 可用空间

### 数据库准备
1. **启动MySQL服务**
   ```bash
   # Linux/macOS
   sudo systemctl start mysql
   # 或
   sudo service mysql start
   
   # Windows
   net start mysql
   ```

2. **创建数据库用户**（可选）
   ```sql
   CREATE USER 'dianfuto'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON *.* TO 'dianfuto'@'localhost';
   FLUSH PRIVILEGES;
   ```

3. **准备数据库信息**
   - 数据库主机：通常是 `localhost`
   - 端口：通常是 `3306`
   - 用户名：如 `root` 或自定义用户
   - 密码：数据库用户密码
   - 数据库名：如 `dianfuto`（安装程序会自动创建）

## 🎯 安装步骤

### 1. 启动项目
```bash
# 克隆项目（如果还没有）
git clone <repository-url>
cd dianfuto

# 安装依赖
yarn install

# 启动开发服务器
yarn dev
```

### 2. 访问安装程序
打开浏览器访问：`http://localhost:3003/install`

### 3. 按照向导完成安装

#### 步骤1：欢迎页面
- 阅读安装说明
- 确认系统要求
- 点击"下一步"

#### 步骤2：数据库配置
- **数据库主机**: `localhost`
- **端口**: `3306`
- **数据库名称**: `dianfuto`
- **用户名**: `root`（或您的数据库用户名）
- **密码**: 您的数据库密码
- 点击"测试数据库连接"验证配置
- 测试成功后点击"下一步"

#### 步骤3：管理员账户
- **用户名**: `admin`（可自定义）
- **密码**: 设置管理员密码（建议8位以上）
- **邮箱**: 管理员邮箱地址
- 点击"下一步"

#### 步骤4：网站配置
- **网站名称**: `滇护通`（可自定义）
- **网站地址**: `http://localhost:3003`（根据实际情况调整）
- **联系邮箱**: 网站联系邮箱
- 点击"下一步"

#### 步骤5：开始安装
- 确认所有配置信息
- 点击"开始安装"
- 等待安装完成（约1-2分钟）

## 📊 安装过程详解

### 自动执行的操作

1. **环境配置文件创建**
   - 生成 `.env.local` 文件
   - 配置数据库连接信息
   - 设置安全密钥

2. **数据库表创建**
   - `settings` - 网站设置表
   - `images` - 图片管理表
   - `cooperation_applications` - 合作申请表
   - `faqs` - 常见问题表
   - `faq_categories` - FAQ分类表
   - `prefecture_status` - 地州服务状态表
   - `admins` - 管理员表
   - `system_logs` - 系统日志表

3. **初始数据插入**
   - 网站基本设置
   - 管理员账户
   - 默认FAQ内容
   - 云南省16个地州的服务状态

4. **目录结构创建**
   - `public/uploads` - 图片上传目录
   - `public/uploads/images` - 图片文件目录
   - `public/uploads/temp` - 临时文件目录
   - `logs` - 日志文件目录
   - `backups` - 备份文件目录

5. **配置文件生成**
   - 上传配置文件
   - robots.txt
   - .htaccess（Apache服务器）
   - 安装完成标记文件

## 🎉 安装完成后

### 访问管理后台
1. 访问：`http://localhost:3003/admin`
2. 使用安装时设置的管理员账户登录
3. 开始配置和使用系统

### 首次配置建议
1. **上传网站Logo和头图**
   - 进入"图片管理"
   - 上传网站Logo和首页头图

2. **上传小程序码**
   - 在"图片管理"中上传小程序码
   - 用户可在前台扫码使用

3. **完善网站信息**
   - 在"网站设置"中完善联系信息
   - 更新关于我们等内容

4. **配置服务地区**
   - 在"地州管理"中更新服务状态
   - 添加联系方式

## 🔧 故障排除

### 常见问题

#### 1. 数据库连接失败
**问题**: "数据库连接失败"
**解决方案**:
- 检查MySQL服务是否启动
- 验证用户名和密码是否正确
- 确认数据库主机和端口配置
- 检查防火墙设置

#### 2. 权限不足
**问题**: "数据库权限不足，无法创建表"
**解决方案**:
- 使用具有CREATE权限的数据库用户
- 或者手动创建数据库：`CREATE DATABASE dianfuto;`

#### 3. 目录创建失败
**问题**: "目录创建失败"
**解决方案**:
- 检查项目目录的写入权限
- 手动创建 `public/uploads` 目录
- 设置适当的目录权限

#### 4. 端口占用
**问题**: "端口3003被占用"
**解决方案**:
- 修改启动端口：`yarn dev -p 3004`
- 或停止占用端口的其他服务

### 重新安装
如果需要重新安装：
1. 删除 `.install.lock` 文件
2. 删除 `.env.local` 文件
3. 清空数据库表
4. 重新访问 `/install` 页面

## 📱 生产环境部署

### 构建项目
```bash
# 构建生产版本
yarn build

# 启动生产服务器
yarn start
```

### 环境变量配置
生产环境需要更新以下配置：
- `NEXTAUTH_URL`: 实际的网站地址
- `SITE_URL`: 网站URL
- 数据库连接信息

### 安全建议
1. **更改默认密码**: 安装后立即更改管理员密码
2. **数据库安全**: 使用专用数据库用户，限制权限
3. **HTTPS配置**: 生产环境启用HTTPS
4. **防火墙设置**: 限制数据库端口访问
5. **定期备份**: 设置数据库自动备份

## 🎯 技术特性

### 安装程序特性
- **可视化界面**: 友好的Web安装向导
- **实时验证**: 数据库连接实时测试
- **进度显示**: 安装进度实时反馈
- **错误处理**: 详细的错误信息和解决建议
- **自动配置**: 一键完成所有配置

### 数据库特性
- **自动创建**: 自动创建数据库和表结构
- **数据初始化**: 预置基础数据和配置
- **字符集支持**: UTF8MB4字符集，支持emoji
- **索引优化**: 合理的数据库索引设计

### 安全特性
- **密码加密**: 管理员密码使用bcrypt加密
- **随机密钥**: 自动生成安全密钥
- **权限控制**: 细粒度的权限管理
- **安装锁定**: 防止重复安装

## 📞 技术支持

如果在安装过程中遇到问题：
1. 查看浏览器控制台错误信息
2. 检查服务器日志
3. 参考本文档的故障排除部分
4. 联系技术支持

---

**滇护通医疗陪护服务平台**  
版本: 1.0.0  
更新时间: 2024年
