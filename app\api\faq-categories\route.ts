import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database'
import { FAQCategory, ApiResponse } from '@/lib/types/database'

// GET - 获取FAQ分类列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const active_only = searchParams.get('active_only') === 'true'
    
    let whereClause = ''
    if (active_only) {
      whereClause = 'WHERE is_active = TRUE'
    }
    
    const categories = await executeQuery<FAQCategory>(
      `SELECT * FROM faq_categories ${whereClause} ORDER BY sort_order ASC, category_name ASC`
    )
    
    const response: ApiResponse<FAQCategory[]> = {
      success: true,
      data: categories
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('获取FAQ分类失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '获取FAQ分类失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
