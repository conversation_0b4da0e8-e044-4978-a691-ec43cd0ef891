-- 云南省地州状态管理数据库表设计
USE dianfuto;

-- 创建地州状态表
CREATE TABLE IF NOT EXISTS prefecture_status (
    id INT PRIMARY KEY AUTO_INCREMENT,
    prefecture_name VARCHAR(100) NOT NULL COMMENT '地州名称',
    prefecture_code VARCHAR(50) NOT NULL UNIQUE COMMENT '地州代码',
    service_status ENUM('未开通', '部分开通', '已开通') DEFAULT '未开通' COMMENT '服务状态',
    color_code VARCHAR(20) DEFAULT '#CCCCCC' COMMENT '对应颜色代码',
    description TEXT COMMENT '状态描述',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_prefecture_code (prefecture_code),
    INDEX idx_service_status (service_status),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='地州服务状态表';

-- 插入云南省16个地州的初始数据
INSERT INTO prefecture_status (prefecture_name, prefecture_code, service_status, color_code, description, sort_order) VALUES
('昆明市', 'kunming', '已开通', '#10B981', '省会城市，服务全面开通', 1),
('曲靖市', 'qujing', '已开通', '#10B981', '重要工业城市，服务已开通', 2),
('玉溪市', 'yuxi', '部分开通', '#FFA500', '正在扩展服务覆盖', 3),
('保山市', 'baoshan', '部分开通', '#FFA500', '边境城市，部分区域已开通', 4),
('昭通市', 'zhaotong', '未开通', '#CCCCCC', '计划中的服务区域', 5),
('丽江市', 'lijiang', '已开通', '#10B981', '旅游城市，服务已开通', 6),
('普洱市', 'puer', '部分开通', '#FFA500', '茶叶之乡，逐步开通中', 7),
('临沧市', 'lincang', '未开通', '#CCCCCC', '边境地区，筹备中', 8),
('楚雄彝族自治州', 'chuxiong', '部分开通', '#FFA500', '自治州，部分县市已开通', 9),
('红河哈尼族彝族自治州', 'honghe', '已开通', '#10B981', '重要自治州，服务已覆盖', 10),
('文山壮族苗族自治州', 'wenshan', '未开通', '#CCCCCC', '边境自治州，规划中', 11),
('西双版纳傣族自治州', 'xishuangbanna', '已开通', '#10B981', '旅游热点，服务已开通', 12),
('大理白族自治州', 'dali', '已开通', '#10B981', '历史文化名城，服务完善', 13),
('德宏傣族景颇族自治州', 'dehong', '部分开通', '#FFA500', '边境自治州，部分开通', 14),
('怒江傈僳族自治州', 'nujiang', '未开通', '#CCCCCC', '山区自治州，筹备中', 15),
('迪庆藏族自治州', 'diqing', '部分开通', '#FFA500', '高原地区，逐步覆盖', 16);

-- 创建地州状态历史记录表（用于追踪状态变更）
CREATE TABLE IF NOT EXISTS prefecture_status_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    prefecture_id INT NOT NULL,
    old_status ENUM('未开通', '部分开通', '已开通'),
    new_status ENUM('未开通', '部分开通', '已开通'),
    changed_by VARCHAR(100) DEFAULT 'admin',
    change_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (prefecture_id) REFERENCES prefecture_status(id) ON DELETE CASCADE,
    INDEX idx_prefecture_id (prefecture_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='地州状态变更历史表';

-- 创建触发器，自动记录状态变更历史
DELIMITER $$
CREATE TRIGGER prefecture_status_change_trigger
    AFTER UPDATE ON prefecture_status
    FOR EACH ROW
BEGIN
    IF OLD.service_status != NEW.service_status THEN
        INSERT INTO prefecture_status_history (
            prefecture_id, 
            old_status, 
            new_status, 
            change_reason
        ) VALUES (
            NEW.id, 
            OLD.service_status, 
            NEW.service_status, 
            CONCAT('状态从 ', OLD.service_status, ' 变更为 ', NEW.service_status)
        );
    END IF;
END$$
DELIMITER ;
