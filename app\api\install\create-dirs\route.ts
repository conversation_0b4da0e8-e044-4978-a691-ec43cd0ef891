import { NextRequest, NextResponse } from 'next/server'
import { mkdirSync, existsSync, writeFileSync, accessSync, constants } from 'fs'
import path from 'path'

// POST - 创建必要的目录
export async function POST(request: NextRequest) {
  try {
    const directories = [
      {
        path: 'public/uploads',
        description: '图片上传目录',
        createGitkeep: true
      },
      {
        path: 'public/uploads/images',
        description: '图片文件目录',
        createGitkeep: true
      },
      {
        path: 'public/uploads/temp',
        description: '临时文件目录',
        createGitkeep: true
      },
      {
        path: 'logs',
        description: '日志文件目录',
        createGitkeep: true
      },
      {
        path: 'backups',
        description: '备份文件目录',
        createGitkeep: true
      }
    ]

    const createdDirs = []
    const errors = []

    for (const dir of directories) {
      try {
        const fullPath = path.join(process.cwd(), dir.path)
        
        // 检查目录是否存在
        if (!existsSync(fullPath)) {
          // 创建目录
          mkdirSync(fullPath, { recursive: true })
          createdDirs.push({
            path: dir.path,
            description: dir.description,
            status: 'created'
          })
        } else {
          createdDirs.push({
            path: dir.path,
            description: dir.description,
            status: 'exists'
          })
        }

        // 检查目录权限
        try {
          accessSync(fullPath, constants.W_OK | constants.R_OK)
        } catch (permError) {
          errors.push(`目录 ${dir.path} 权限不足`)
          continue
        }

        // 创建 .gitkeep 文件（如果需要）
        if (dir.createGitkeep) {
          const gitkeepPath = path.join(fullPath, '.gitkeep')
          if (!existsSync(gitkeepPath)) {
            writeFileSync(gitkeepPath, '# This file keeps the directory in git\n', 'utf8')
          }
        }

      } catch (error: any) {
        errors.push(`创建目录 ${dir.path} 失败: ${error.message}`)
      }
    }

    // 创建上传配置文件
    try {
      const uploadConfigPath = path.join(process.cwd(), 'public/uploads/config.json')
      const uploadConfig = {
        maxFileSize: 10485760, // 10MB
        allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
        uploadPath: '/uploads',
        createdAt: new Date().toISOString()
      }
      
      if (!existsSync(uploadConfigPath)) {
        writeFileSync(uploadConfigPath, JSON.stringify(uploadConfig, null, 2), 'utf8')
      }
    } catch (error) {
      errors.push('创建上传配置文件失败')
    }

    // 创建 robots.txt
    try {
      const robotsPath = path.join(process.cwd(), 'public/robots.txt')
      const robotsContent = `User-agent: *
Allow: /

# 禁止访问管理后台
Disallow: /admin
Disallow: /api/admin

# 禁止访问上传目录中的敏感文件
Disallow: /uploads/temp

Sitemap: ${process.env.SITE_URL || 'http://localhost:3003'}/sitemap.xml
`
      
      if (!existsSync(robotsPath)) {
        writeFileSync(robotsPath, robotsContent, 'utf8')
      }
    } catch (error) {
      errors.push('创建 robots.txt 失败')
    }

    // 创建 .htaccess（如果是Apache服务器）
    try {
      const htaccessPath = path.join(process.cwd(), 'public/.htaccess')
      const htaccessContent = `# 安全配置
<Files ".env*">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

# 缓存配置
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# Gzip压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
`
      
      if (!existsSync(htaccessPath)) {
        writeFileSync(htaccessPath, htaccessContent, 'utf8')
      }
    } catch (error) {
      // .htaccess 创建失败不是致命错误
    }

    if (errors.length > 0) {
      return NextResponse.json({
        success: false,
        error: '部分目录创建失败',
        errors,
        createdDirs
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: '目录创建成功',
      createdDirs,
      totalDirs: createdDirs.length
    })
    
  } catch (error: any) {
    console.error('创建目录失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error.message || '创建目录失败'
    }, { status: 500 })
  }
}
