"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Heart, Settings, ImageIcon, Info, Save, Upload, Eye, ArrowLeft, Loader2, Plus, Edit, Trash2, MessageCircle, Map, Circle, Phone } from "lucide-react"
import { settingsApi, cooperationApi, uploadApi, faqApi, prefectureStatusApi, utils } from "@/lib/api-client"
import { CooperationApplication, FAQ, FAQFormData, PrefectureStatus, PrefectureStatusFormData, ServiceStatus } from "@/lib/types/database"

export default function AdminPage() {
  // 登录状态管理
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [loginForm, setLoginForm] = useState({ username: '', password: '' })
  const [loginError, setLoginError] = useState('')
  const [showLogin, setShowLogin] = useState(true)

  const [siteSettings, setSiteSettings] = useState({
    site_name: "滇护通",
    hero_title: "让医疗陪护更简单、更安心",
    hero_subtitle: "足不出户，为家人预约专业的陪诊、医疗跑腿、代办、上门护工等服务",
    hero_image: "/placeholder.svg?height=600&width=1200",
    site_logo: "/placeholder.svg?height=60&width=200",
    miniprogram_qrcode: "",
    about_us: "滇护通是一家专业的医疗陪护服务平台，致力于为用户提供便捷、可靠的医疗陪护服务。我们拥有专业的陪护团队和完善的服务体系，让您的家人享受到最贴心的医疗陪护服务。",
    copyright: "2024 滇护通医疗陪护服务平台. 保留所有权利.",
    icp: "滇ICP备2024000001号",
    phone: "************",
    address: "云南省昆明市五华区春城路100号",
    email: "<EMAIL>"
  })

  const [cooperationData, setCooperationData] = useState<CooperationApplication[]>([])
  const [faqData, setFaqData] = useState<FAQ[]>([])
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [editingFaq, setEditingFaq] = useState<FAQ | null>(null)
  const [showFaqForm, setShowFaqForm] = useState(false)
  const [faqForm, setFaqForm] = useState<FAQFormData>({
    question: '',
    answer: '',
    category: 'general',
    sort_order: 0,
    is_active: true
  })
  const [prefectureData, setPrefectureData] = useState<PrefectureStatus[]>([])
  const [editingPrefecture, setEditingPrefecture] = useState<PrefectureStatus | null>(null)
  const [showPrefectureForm, setShowPrefectureForm] = useState(false)
  const [prefectureForm, setPrefectureForm] = useState<PrefectureStatusFormData>({
    prefecture_name: '',
    prefecture_code: '',
    service_status: '未开通',
    description: '',
    sort_order: 0,
    is_active: true
  })

  // 服务管理相关状态
  const [availableServices, setAvailableServices] = useState<string[]>([])
  const [prefectureServices, setPrefectureServices] = useState<{[key: string]: string[]}>({})
  const [cooperationEnabled, setCooperationEnabled] = useState<{[key: string]: boolean}>({})
  const [newServiceName, setNewServiceName] = useState('')

  // 登录验证函数
  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault()
    setLoginError('')

    // 固定的用户名和密码
    const ADMIN_USERNAME = 'admin'
    const ADMIN_PASSWORD = 'admin123456'

    if (loginForm.username === ADMIN_USERNAME && loginForm.password === ADMIN_PASSWORD) {
      setIsLoggedIn(true)
      setShowLogin(false)
      // 保存登录状态到localStorage
      localStorage.setItem('admin_logged_in', 'true')
      localStorage.setItem('admin_login_time', Date.now().toString())
    } else {
      setLoginError('用户名或密码错误')
    }
  }

  // 登出函数
  const handleLogout = () => {
    setIsLoggedIn(false)
    setShowLogin(true)
    setLoginForm({ username: '', password: '' })
    localStorage.removeItem('admin_logged_in')
    localStorage.removeItem('admin_login_time')
  }

  // 检查登录状态
  useEffect(() => {
    const savedLoginState = localStorage.getItem('admin_logged_in')
    const loginTime = localStorage.getItem('admin_login_time')

    if (savedLoginState === 'true' && loginTime) {
      const now = Date.now()
      const loginTimestamp = parseInt(loginTime)
      const twentyFourHours = 24 * 60 * 60 * 1000 // 24小时

      // 检查是否在24小时内
      if (now - loginTimestamp < twentyFourHours) {
        setIsLoggedIn(true)
        setShowLogin(false)
      } else {
        // 登录已过期
        localStorage.removeItem('admin_logged_in')
        localStorage.removeItem('admin_login_time')
      }
    }
  }, [])

  // 加载网站设置
  const loadSettings = async () => {
    setLoading(true)
    try {
      const response = await settingsApi.getAll()
      if (response.success && response.data) {
        setSiteSettings({
          site_name: response.data.site_name || "",
          hero_title: response.data.hero_title || "",
          hero_subtitle: response.data.hero_subtitle || "",
          hero_image: response.data.hero_image || "",
          site_logo: response.data.site_logo || "",
          miniprogram_qrcode: response.data.miniprogram_qrcode || "",
          about_us: response.data.about_us || "",
          copyright: response.data.copyright || "",
          icp: response.data.icp || "",
          phone: response.data.phone || "",
          address: response.data.address || "",
          email: response.data.email || ""
        })
      }
    } catch (error) {
      console.error('加载设置失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 加载合作申请
  const loadCooperationData = async () => {
    setLoading(true)
    try {
      const response = await cooperationApi.getList({ limit: 50 })
      if (response.success && response.data) {
        setCooperationData(response.data.data)
      }
    } catch (error) {
      console.error('加载合作申请失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 加载FAQ数据
  const loadFaqData = async () => {
    setLoading(true)
    try {
      const response = await faqApi.getList({ limit: 50 })
      if (response.success && response.data) {
        setFaqData(response.data.data)
      }
    } catch (error) {
      console.error('加载FAQ失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 加载地州数据
  const loadPrefectureData = async () => {
    setLoading(true)
    try {
      const response = await prefectureStatusApi.getList(false) // 获取所有数据，包括禁用的
      if (response.success && response.data) {
        setPrefectureData(response.data)
      }
    } catch (error) {
      console.error('加载地州数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 页面加载时获取数据
  useEffect(() => {
    loadSettings()
    loadCooperationData()
    loadFaqData()
    loadPrefectureData()
  }, [])

  const handleSaveSettings = async () => {
    setSaving(true)
    try {
      const response = await settingsApi.updateAll(siteSettings)
      if (response.success) {
        alert("设置已保存！")
      } else {
        alert("保存失败：" + response.error)
      }
    } catch (error) {
      console.error('保存设置失败:', error)
      alert("保存失败，请重试")
    } finally {
      setSaving(false)
    }
  }

  const handleImageUpload = (field: string) => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      setUploading(true)
      try {
        const response = await uploadApi.uploadImage(file, field, field)
        if (response.success && response.data) {
          // 更新本地状态
          setSiteSettings(prev => ({
            ...prev,
            [field]: response.data!.url
          }))
          alert('图片上传成功！')
        } else {
          alert('上传失败：' + response.error)
        }
      } catch (error) {
        console.error('图片上传失败:', error)
        alert('上传失败，请重试')
      } finally {
        setUploading(false)
      }
    }
    input.click()
  }

  const handleCooperationStatus = async (id: number, status: string) => {
    try {
      const response = await cooperationApi.updateStatus(id, status)
      if (response.success) {
        // 更新本地状态
        setCooperationData(prev =>
          prev.map(item =>
            item.id === id ? { ...item, status: status as any } : item
          )
        )
        alert(`状态更新成功`)
      } else {
        alert("更新失败：" + response.error)
      }
    } catch (error) {
      console.error('更新状态失败:', error)
      alert("更新失败，请重试")
    }
  }

  // FAQ管理函数
  const handleCreateFaq = async () => {
    setSaving(true)
    try {
      const response = await faqApi.create(faqForm)
      if (response.success) {
        alert('FAQ创建成功！')
        setShowFaqForm(false)
        resetFaqForm()
        loadFaqData()
      } else {
        alert('创建失败：' + response.error)
      }
    } catch (error) {
      console.error('创建FAQ失败:', error)
      alert('创建失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  const handleUpdateFaq = async () => {
    if (!editingFaq) return
    setSaving(true)
    try {
      const response = await faqApi.update(editingFaq.id, faqForm)
      if (response.success) {
        alert('FAQ更新成功！')
        setEditingFaq(null)
        setShowFaqForm(false)
        resetFaqForm()
        loadFaqData()
      } else {
        alert('更新失败：' + response.error)
      }
    } catch (error) {
      console.error('更新FAQ失败:', error)
      alert('更新失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  const handleDeleteFaq = async (id: number) => {
    if (!confirm('确定要删除这个FAQ吗？')) return
    setSaving(true)
    try {
      const response = await faqApi.delete(id)
      if (response.success) {
        alert('FAQ删除成功！')
        loadFaqData()
      } else {
        alert('删除失败：' + response.error)
      }
    } catch (error) {
      console.error('删除FAQ失败:', error)
      alert('删除失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  const handleEditFaq = (faq: FAQ) => {
    setEditingFaq(faq)
    setFaqForm({
      question: faq.question,
      answer: faq.answer,
      category: faq.category,
      sort_order: faq.sort_order,
      is_active: faq.is_active
    })
    setShowFaqForm(true)
  }

  const resetFaqForm = () => {
    setFaqForm({
      question: '',
      answer: '',
      category: 'general',
      sort_order: 0,
      is_active: true
    })
    setEditingFaq(null)
  }

  // 地州状态管理函数
  const handleUpdatePrefectureStatus = async (id: number, newStatus: ServiceStatus) => {
    setSaving(true)
    try {
      const response = await prefectureStatusApi.batchUpdate([{ id, service_status: newStatus }])
      if (response.success) {
        alert('地州状态更新成功！')
        loadPrefectureData()
      } else {
        alert('更新失败：' + response.error)
      }
    } catch (error) {
      console.error('更新地州状态失败:', error)
      alert('更新失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  const handleCreatePrefecture = async () => {
    setSaving(true)
    try {
      const response = await prefectureStatusApi.create(prefectureForm)
      if (response.success) {
        alert('地州创建成功！')
        setShowPrefectureForm(false)
        resetPrefectureForm()
        loadPrefectureData()
      } else {
        alert('创建失败：' + response.error)
      }
    } catch (error) {
      console.error('创建地州失败:', error)
      alert('创建失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  const handleUpdatePrefecture = async () => {
    if (!editingPrefecture) return
    setSaving(true)
    try {
      const response = await prefectureStatusApi.update(editingPrefecture.id, prefectureForm)
      if (response.success) {
        alert('地州更新成功！')
        setEditingPrefecture(null)
        setShowPrefectureForm(false)
        resetPrefectureForm()
        loadPrefectureData()
      } else {
        alert('更新失败：' + response.error)
      }
    } catch (error) {
      console.error('更新地州失败:', error)
      alert('更新失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  const handleDeletePrefecture = async (id: number) => {
    if (!confirm('确定要删除这个地州吗？')) return
    setSaving(true)
    try {
      const response = await prefectureStatusApi.delete(id)
      if (response.success) {
        alert('地州删除成功！')
        loadPrefectureData()
      } else {
        alert('删除失败：' + response.error)
      }
    } catch (error) {
      console.error('删除地州失败:', error)
      alert('删除失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  const handleEditPrefecture = (prefecture: PrefectureStatus) => {
    setEditingPrefecture(prefecture)
    setPrefectureForm({
      prefecture_name: prefecture.prefecture_name,
      prefecture_code: prefecture.prefecture_code,
      service_status: prefecture.service_status,
      description: prefecture.description || '',
      sort_order: prefecture.sort_order,
      is_active: prefecture.is_active
    })
    // 加载该地州的服务配置
    loadPrefectureConfig(prefecture.prefecture_code)
    setShowPrefectureForm(true)
  }

  const resetPrefectureForm = () => {
    setPrefectureForm({
      prefecture_name: '',
      prefecture_code: '',
      service_status: '未开通',
      description: '',
      sort_order: 0,
      is_active: true
    })
    setEditingPrefecture(null)
  }

  // 服务管理函数
  const handleAddService = () => {
    if (newServiceName.trim() && !availableServices.includes(newServiceName.trim())) {
      setAvailableServices(prev => [...prev, newServiceName.trim()])
      setNewServiceName('')
    }
  }

  const handleRemoveService = (serviceName: string) => {
    setAvailableServices(prev => prev.filter(s => s !== serviceName))
    // 同时从所有地州的服务中移除
    setPrefectureServices(prev => {
      const updated = { ...prev }
      Object.keys(updated).forEach(prefectureCode => {
        updated[prefectureCode] = updated[prefectureCode].filter(s => s !== serviceName)
      })
      return updated
    })
  }

  const handleTogglePrefectureService = (prefectureCode: string, serviceName: string) => {
    setPrefectureServices(prev => {
      const currentServices = prev[prefectureCode] || []
      const updated = { ...prev }

      if (currentServices.includes(serviceName)) {
        updated[prefectureCode] = currentServices.filter(s => s !== serviceName)
      } else {
        updated[prefectureCode] = [...currentServices, serviceName]
      }

      return updated
    })
  }

  const handleToggleCooperation = (prefectureCode: string) => {
    setCooperationEnabled(prev => ({
      ...prev,
      [prefectureCode]: !prev[prefectureCode]
    }))
  }

  // 保存地州服务配置
  const handleSavePrefectureConfig = async (prefectureCode: string) => {
    try {
      // 这里应该调用API保存配置，目前使用localStorage模拟
      const config = {
        services: prefectureServices[prefectureCode] || [],
        cooperationEnabled: cooperationEnabled[prefectureCode] || false
      }
      localStorage.setItem(`prefecture_config_${prefectureCode}`, JSON.stringify(config))
      alert('配置保存成功！')
    } catch (error) {
      console.error('保存配置失败:', error)
      alert('保存失败，请重试')
    }
  }

  // 加载地州服务配置
  const loadPrefectureConfig = (prefectureCode: string) => {
    try {
      const saved = localStorage.getItem(`prefecture_config_${prefectureCode}`)
      if (saved) {
        const config = JSON.parse(saved)
        setPrefectureServices(prev => ({
          ...prev,
          [prefectureCode]: config.services || []
        }))
        setCooperationEnabled(prev => ({
          ...prev,
          [prefectureCode]: config.cooperationEnabled || false
        }))
      }
    } catch (error) {
      console.error('加载配置失败:', error)
    }
  }

  // 初始化默认服务
  useEffect(() => {
    if (availableServices.length === 0) {
      setAvailableServices([
        '陪诊服务',
        '医疗跑腿',
        '上门护理',
        '代办服务',
        '健康咨询',
        '康复指导',
        '心理疏导',
        '营养配餐'
      ])
    }
  }, [availableServices.length])

  // 如果未登录，显示登录页面
  if (showLogin || !isLoggedIn) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="space-y-1 pb-6">
            <div className="text-center mb-4">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl shadow-lg mb-4">
                <Heart className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">滇护通</h1>
              <p className="text-gray-600">管理后台登录</p>
            </div>
            <CardTitle className="text-2xl font-bold text-center text-gray-900">
              管理员登录
            </CardTitle>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              {loginError && (
                <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg text-sm">
                  {loginError}
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="username" className="text-sm font-medium text-gray-700">
                  用户名
                </Label>
                <Input
                  id="username"
                  type="text"
                  value={loginForm.username}
                  onChange={(e) => setLoginForm(prev => ({ ...prev, username: e.target.value }))}
                  placeholder="请输入用户名"
                  className="border-gray-200 focus:border-emerald-500 focus:ring-emerald-500"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                  密码
                </Label>
                <Input
                  id="password"
                  type="password"
                  value={loginForm.password}
                  onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
                  placeholder="请输入密码"
                  className="border-gray-200 focus:border-emerald-500 focus:ring-emerald-500"
                  required
                />
              </div>

              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white font-medium py-2.5 transition-all duration-200"
              >
                登录
              </Button>
            </form>

            <div className="mt-6 pt-6 border-t border-gray-100">
              <div className="text-center text-sm text-gray-500">
                <p>默认账户信息：</p>
                <p className="mt-1">用户名：<span className="font-mono text-gray-700">admin</span></p>
                <p>密码：<span className="font-mono text-gray-700">admin123456</span></p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50">
      {/* Header */}
      <header className="border-b border-emerald-100 bg-white/90 backdrop-blur-md sticky top-0 z-50 shadow-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
              <Heart className="w-6 h-6 text-white" />
            </div>
            <span className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
              滇护通 - 管理后台
            </span>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              className="border-emerald-200 text-emerald-700 hover:bg-emerald-50 bg-transparent"
            >
              <Eye className="w-4 h-4 mr-2" />
              <a href="/" target="_blank" rel="noreferrer">
                预览网站
              </a>
            </Button>
            <Button
              variant="outline"
              className="border-emerald-200 text-emerald-700 hover:bg-emerald-50 bg-transparent"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              <a href="/">返回首页</a>
            </Button>
            <Button
              onClick={handleLogout}
              variant="outline"
              className="border-red-200 text-red-700 hover:bg-red-50 bg-transparent"
            >
              退出登录
            </Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">网站管理后台</h1>
          <p className="text-gray-600">管理网站内容、设置和合作咨询</p>
        </div>

        <Tabs defaultValue="settings" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5 bg-emerald-50 border border-emerald-200">
            <TabsTrigger value="settings" className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white">
              <Settings className="w-4 h-4 mr-2" />
              网站设置
            </TabsTrigger>
            <TabsTrigger value="images" className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white">
              <ImageIcon className="w-4 h-4 mr-2" />
              图片管理
            </TabsTrigger>
            <TabsTrigger value="faqs" className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white">
              <MessageCircle className="w-4 h-4 mr-2" />
              常见问题
            </TabsTrigger>
            <TabsTrigger value="prefecture" className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white">
              <Map className="w-4 h-4 mr-2" />
              地图管理
            </TabsTrigger>
            <TabsTrigger
              value="cooperation"
              className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white"
            >
              <Info className="w-4 h-4 mr-2" />
              合作咨询
            </TabsTrigger>
          </TabsList>

          {/* 网站设置 */}
          <TabsContent value="settings">
            <div className="grid lg:grid-cols-2 gap-8">
              <Card className="border-0 shadow-xl">
                <CardHeader>
                  <CardTitle className="text-emerald-700">基本信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="siteName">网站名称</Label>
                    <Input
                      id="siteName"
                      value={siteSettings.site_name}
                      onChange={(e) => setSiteSettings((prev) => ({ ...prev, site_name: e.target.value }))}
                      className="border-emerald-200 focus:border-emerald-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="heroTitle">首页标题</Label>
                    <Input
                      id="heroTitle"
                      value={siteSettings.hero_title}
                      onChange={(e) => setSiteSettings((prev) => ({ ...prev, hero_title: e.target.value }))}
                      className="border-emerald-200 focus:border-emerald-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="heroSubtitle">首页副标题</Label>
                    <Textarea
                      id="heroSubtitle"
                      value={siteSettings.hero_subtitle}
                      onChange={(e) => setSiteSettings((prev) => ({ ...prev, hero_subtitle: e.target.value }))}
                      className="border-emerald-200 focus:border-emerald-500"
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">联系电话</Label>
                    <Input
                      id="phone"
                      value={siteSettings.phone}
                      onChange={(e) => setSiteSettings((prev) => ({ ...prev, phone: e.target.value }))}
                      className="border-emerald-200 focus:border-emerald-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="address">公司地址</Label>
                    <Input
                      id="address"
                      value={siteSettings.address}
                      onChange={(e) => setSiteSettings((prev) => ({ ...prev, address: e.target.value }))}
                      className="border-emerald-200 focus:border-emerald-500"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-xl">
                <CardHeader>
                  <CardTitle className="text-emerald-700">页脚信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="aboutUs">关于我们</Label>
                    <Textarea
                      id="aboutUs"
                      value={siteSettings.about_us}
                      onChange={(e) => setSiteSettings((prev) => ({ ...prev, about_us: e.target.value }))}
                      className="border-emerald-200 focus:border-emerald-500"
                      rows={4}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="copyright">版权信息</Label>
                    <Input
                      id="copyright"
                      value={siteSettings.copyright}
                      onChange={(e) => setSiteSettings((prev) => ({ ...prev, copyright: e.target.value }))}
                      className="border-emerald-200 focus:border-emerald-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="icp">备案号</Label>
                    <Input
                      id="icp"
                      value={siteSettings.icp}
                      onChange={(e) => setSiteSettings((prev) => ({ ...prev, icp: e.target.value }))}
                      className="border-emerald-200 focus:border-emerald-500"
                      placeholder="如：滇ICP备2024000001号"
                    />
                  </div>
                  <Button
                    onClick={handleSaveSettings}
                    disabled={saving}
                    className="w-full bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50"
                  >
                    {saving ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="w-4 h-4 mr-2" />
                    )}
                    {saving ? "保存中..." : "保存设置"}
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 图片管理 */}
          <TabsContent value="images">
            <Card className="border-0 shadow-xl">
              <CardHeader>
                <CardTitle className="text-emerald-700">网站图片管理</CardTitle>
                <p className="text-gray-600">管理网站首页头图和其他重要图片</p>
              </CardHeader>
              <CardContent className="space-y-8">
                <div className="grid md:grid-cols-3 gap-8">
                  <div className="space-y-4">
                    <Label>首页头图</Label>
                    <div className="border-2 border-dashed border-emerald-200 rounded-lg p-6 text-center hover:border-emerald-300 transition-colors">
                      <img
                        src={siteSettings.hero_image || "/placeholder.svg"}
                        alt="首页头图预览"
                        className="w-full h-32 object-cover rounded-lg mb-4"
                      />
                      <Button
                        onClick={() => handleImageUpload("hero_image")}
                        variant="outline"
                        disabled={uploading}
                        className="border-emerald-200 text-emerald-700 hover:bg-emerald-50 disabled:opacity-50"
                      >
                        {uploading ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <Upload className="w-4 h-4 mr-2" />
                        )}
                        {uploading ? "上传中..." : "上传新图片"}
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <Label>网站Logo</Label>
                    <div className="border-2 border-dashed border-emerald-200 rounded-lg p-6 text-center hover:border-emerald-300 transition-colors">
                      <img
                        src={siteSettings.site_logo || "/placeholder.svg?height=60&width=200"}
                        alt="网站Logo预览"
                        className="w-auto h-16 object-contain mx-auto rounded-lg mb-4"
                      />
                      <Button
                        onClick={() => handleImageUpload("site_logo")}
                        variant="outline"
                        disabled={uploading}
                        className="border-emerald-200 text-emerald-700 hover:bg-emerald-50 disabled:opacity-50"
                      >
                        {uploading ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <Upload className="w-4 h-4 mr-2" />
                        )}
                        {uploading ? "上传中..." : "上传新Logo"}
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <Label>小程序码</Label>
                    <div className="border-2 border-dashed border-emerald-200 rounded-lg p-6 text-center hover:border-emerald-300 transition-colors">
                      {siteSettings.miniprogram_qrcode ? (
                        <img
                          src={siteSettings.miniprogram_qrcode}
                          alt="小程序码预览"
                          className="w-32 h-32 object-contain mx-auto rounded-lg mb-4 border"
                        />
                      ) : (
                        <div className="w-32 h-32 mx-auto mb-4 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                          <div className="text-center">
                            <Phone className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                            <p className="text-xs text-gray-400">暂无小程序码</p>
                          </div>
                        </div>
                      )}
                      <Button
                        onClick={() => handleImageUpload("miniprogram_qrcode")}
                        variant="outline"
                        disabled={uploading}
                        className="border-emerald-200 text-emerald-700 hover:bg-emerald-50 disabled:opacity-50"
                      >
                        {uploading ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <Upload className="w-4 h-4 mr-2" />
                        )}
                        {uploading ? "上传中..." : "上传小程序码"}
                      </Button>
                    </div>
                  </div>
                </div>
                <div className="bg-emerald-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-emerald-800 mb-2">图片要求</h4>
                  <ul className="text-sm text-emerald-700 space-y-1">
                    <li>• 首页头图建议尺寸：1200x600像素</li>
                    <li>• 小程序码建议尺寸：430x430像素（正方形）</li>
                    <li>• 图片格式：JPG、PNG、WebP</li>
                    <li>• 文件大小：不超过2MB</li>
                    <li>• 小程序码应清晰可扫描，建议白色背景</li>
                    <li>• 建议使用高质量、与医疗陪护相关的图片</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 合作咨询 */}
          <TabsContent value="cooperation">
            <Card className="border-0 shadow-xl">
              <CardHeader>
                <CardTitle className="text-emerald-700">合作咨询管理</CardTitle>
                <p className="text-gray-600">查看和处理用户提交的合作意向</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {cooperationData.map((item) => (
                    <Card key={item.id} className="border border-emerald-100 hover:shadow-lg transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900">{item.name}</h3>
                            <p className="text-gray-600">{item.company}</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <span
                              className={`px-3 py-1 rounded-full text-sm font-medium ${utils.getStatusClass(item.status)}`}
                            >
                              {utils.getStatusText(item.status)}
                            </span>
                          </div>
                        </div>
                        <div className="grid md:grid-cols-2 gap-4 mb-4">
                          <div>
                            <span className="text-sm text-gray-500">联系方式：</span>
                            <span className="text-gray-900">{item.phone}</span>
                          </div>
                          <div>
                            <span className="text-sm text-gray-500">邮箱：</span>
                            <span className="text-gray-900">{item.email}</span>
                          </div>
                          <div>
                            <span className="text-sm text-gray-500">合作区域：</span>
                            <span className="text-gray-900">{item.region}</span>
                          </div>
                          <div>
                            <span className="text-sm text-gray-500">合作类型：</span>
                            <span className="text-gray-900">{item.cooperation_type}</span>
                          </div>
                        </div>
                        <div className="mb-4">
                          <span className="text-sm text-gray-500">合作意向：</span>
                          <p className="text-gray-900 mt-1">{item.message}</p>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-500">提交时间：{utils.formatDate(item.created_at)}</span>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleCooperationStatus(item.id, "contacted")}
                              className="border-emerald-200 text-emerald-700 hover:bg-emerald-50"
                              disabled={item.status === 'contacted' || item.status === 'completed'}
                            >
                              标记已联系
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => handleCooperationStatus(item.id, "completed")}
                              className="bg-emerald-600 hover:bg-emerald-700 text-white"
                              disabled={item.status === 'completed'}
                            >
                              标记完成
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* FAQ管理 */}
          <TabsContent value="faqs">
            <Card className="border-emerald-200 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-teal-50 border-b border-emerald-200">
                <CardTitle className="text-emerald-800 flex items-center gap-2">
                  <MessageCircle className="w-5 h-5" />
                  常见问题管理
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">FAQ列表</h3>
                  <Button
                    onClick={() => {
                      resetFaqForm()
                      setShowFaqForm(true)
                    }}
                    className="bg-emerald-600 hover:bg-emerald-700 text-white"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    添加FAQ
                  </Button>
                </div>

                {/* FAQ表单 */}
                {showFaqForm && (
                  <Card className="mb-6 border-emerald-200">
                    <CardHeader>
                      <CardTitle className="text-lg">
                        {editingFaq ? '编辑FAQ' : '添加新FAQ'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label htmlFor="faq-question">问题 *</Label>
                        <Input
                          id="faq-question"
                          value={faqForm.question}
                          onChange={(e) => setFaqForm(prev => ({ ...prev, question: e.target.value }))}
                          placeholder="请输入问题"
                          className="border-emerald-200 focus:border-emerald-500"
                        />
                      </div>
                      <div>
                        <Label htmlFor="faq-answer">答案 *</Label>
                        <Textarea
                          id="faq-answer"
                          value={faqForm.answer}
                          onChange={(e) => setFaqForm(prev => ({ ...prev, answer: e.target.value }))}
                          placeholder="请输入答案"
                          rows={4}
                          className="border-emerald-200 focus:border-emerald-500"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="faq-category">分类</Label>
                          <Input
                            id="faq-category"
                            value={faqForm.category}
                            onChange={(e) => setFaqForm(prev => ({ ...prev, category: e.target.value }))}
                            placeholder="如：service, pricing等"
                            className="border-emerald-200 focus:border-emerald-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="faq-sort">排序</Label>
                          <Input
                            id="faq-sort"
                            type="number"
                            value={faqForm.sort_order}
                            onChange={(e) => setFaqForm(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}
                            placeholder="数字越小越靠前"
                            className="border-emerald-200 focus:border-emerald-500"
                          />
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="faq-active"
                          checked={faqForm.is_active}
                          onChange={(e) => setFaqForm(prev => ({ ...prev, is_active: e.target.checked }))}
                          className="rounded border-emerald-300 text-emerald-600 focus:ring-emerald-500"
                        />
                        <Label htmlFor="faq-active">启用显示</Label>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          onClick={editingFaq ? handleUpdateFaq : handleCreateFaq}
                          disabled={saving || !faqForm.question || !faqForm.answer}
                          className="bg-emerald-600 hover:bg-emerald-700 text-white"
                        >
                          {saving ? (
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          ) : (
                            <Save className="w-4 h-4 mr-2" />
                          )}
                          {saving ? '保存中...' : (editingFaq ? '更新' : '创建')}
                        </Button>
                        <Button
                          onClick={() => {
                            setShowFaqForm(false)
                            resetFaqForm()
                          }}
                          variant="outline"
                          className="border-emerald-200 text-emerald-700 hover:bg-emerald-50"
                        >
                          取消
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* FAQ列表 */}
                <div className="space-y-4">
                  {faqData.map((faq) => (
                    <Card key={faq.id} className="border-emerald-100">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 mb-2">{faq.question}</h4>
                            <p className="text-gray-600 text-sm mb-2 line-clamp-2">{faq.answer}</p>
                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              <span>分类: {faq.category}</span>
                              <span>排序: {faq.sort_order}</span>
                              <span>查看: {faq.view_count}次</span>
                              <span className={`px-2 py-1 rounded ${faq.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                                {faq.is_active ? '已启用' : '已禁用'}
                              </span>
                            </div>
                          </div>
                          <div className="flex gap-2 ml-4">
                            <Button
                              onClick={() => handleEditFaq(faq)}
                              size="sm"
                              variant="outline"
                              className="border-emerald-200 text-emerald-700 hover:bg-emerald-50"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              onClick={() => handleDeleteFaq(faq.id)}
                              size="sm"
                              variant="outline"
                              className="border-red-200 text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 地州管理 */}
          <TabsContent value="prefecture">
            <Card className="border-emerald-200 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-teal-50 border-b border-emerald-200">
                <CardTitle className="text-emerald-800 flex items-center gap-2">
                  <Map className="w-5 h-5" />
                  地州状态管理
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                {/* 服务类型管理 */}
                <Card className="mb-6 border-blue-200">
                  <CardHeader>
                    <CardTitle className="text-lg text-blue-800">服务类型管理</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex gap-2 mb-4">
                      <Input
                        value={newServiceName}
                        onChange={(e) => setNewServiceName(e.target.value)}
                        placeholder="输入新服务名称"
                        className="border-blue-200 focus:border-blue-500"
                        onKeyPress={(e) => e.key === 'Enter' && handleAddService()}
                      />
                      <Button
                        onClick={handleAddService}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        添加服务
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {availableServices.map(service => (
                        <div key={service} className="flex items-center gap-1 bg-blue-50 px-3 py-1 rounded-full">
                          <span className="text-sm text-blue-800">{service}</span>
                          <Button
                            onClick={() => handleRemoveService(service)}
                            size="sm"
                            variant="ghost"
                            className="h-4 w-4 p-0 text-blue-600 hover:text-red-600"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">地州列表</h3>
                  <Button
                    onClick={() => {
                      resetPrefectureForm()
                      setShowPrefectureForm(true)
                    }}
                    className="bg-emerald-600 hover:bg-emerald-700 text-white"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    添加地州
                  </Button>
                </div>

                {/* 地州表单 */}
                {showPrefectureForm && (
                  <Card className="mb-6 border-emerald-200">
                    <CardHeader>
                      <CardTitle className="text-lg">
                        {editingPrefecture ? '编辑地州' : '添加新地州'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="prefecture-name">地州名称 *</Label>
                          <Input
                            id="prefecture-name"
                            value={prefectureForm.prefecture_name}
                            onChange={(e) => setPrefectureForm(prev => ({ ...prev, prefecture_name: e.target.value }))}
                            placeholder="如：昆明市"
                            className="border-emerald-200 focus:border-emerald-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="prefecture-code">地州代码 *</Label>
                          <Input
                            id="prefecture-code"
                            value={prefectureForm.prefecture_code}
                            onChange={(e) => setPrefectureForm(prev => ({ ...prev, prefecture_code: e.target.value }))}
                            placeholder="如：kunming"
                            className="border-emerald-200 focus:border-emerald-500"
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="prefecture-status">服务状态 *</Label>
                          <select
                            id="prefecture-status"
                            value={prefectureForm.service_status}
                            onChange={(e) => setPrefectureForm(prev => ({ ...prev, service_status: e.target.value as ServiceStatus }))}
                            className="w-full px-3 py-2 border border-emerald-200 rounded-md focus:border-emerald-500 focus:outline-none"
                          >
                            <option value="未开通">未开通</option>
                            <option value="部分开通">部分开通</option>
                            <option value="已开通">已开通</option>
                          </select>
                        </div>
                        <div>
                          <Label htmlFor="prefecture-sort">排序</Label>
                          <Input
                            id="prefecture-sort"
                            type="number"
                            value={prefectureForm.sort_order}
                            onChange={(e) => setPrefectureForm(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}
                            placeholder="数字越小越靠前"
                            className="border-emerald-200 focus:border-emerald-500"
                          />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="prefecture-desc">描述</Label>
                        <Textarea
                          id="prefecture-desc"
                          value={prefectureForm.description}
                          onChange={(e) => setPrefectureForm(prev => ({ ...prev, description: e.target.value }))}
                          placeholder="地州状态描述"
                          rows={3}
                          className="border-emerald-200 focus:border-emerald-500"
                        />
                      </div>

                      {/* 服务管理 */}
                      {editingPrefecture && (
                        <div className="border-t pt-4">
                          <Label className="text-base font-semibold">可用服务管理</Label>
                          <div className="mt-2 space-y-2">
                            {availableServices.map(service => (
                              <div key={service} className="flex items-center space-x-2">
                                <input
                                  type="checkbox"
                                  id={`service-${service}`}
                                  checked={(prefectureServices[editingPrefecture.prefecture_code] || []).includes(service)}
                                  onChange={() => handleTogglePrefectureService(editingPrefecture.prefecture_code, service)}
                                  className="rounded border-emerald-300 text-emerald-600 focus:ring-emerald-500"
                                />
                                <Label htmlFor={`service-${service}`} className="text-sm">{service}</Label>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* 合作机会开关 */}
                      {editingPrefecture && (
                        <div className="border-t pt-4">
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id="cooperation-enabled"
                              checked={cooperationEnabled[editingPrefecture.prefecture_code] || false}
                              onChange={() => handleToggleCooperation(editingPrefecture.prefecture_code)}
                              className="rounded border-emerald-300 text-emerald-600 focus:ring-emerald-500"
                            />
                            <Label htmlFor="cooperation-enabled" className="text-base font-semibold">开启合作机会</Label>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">开启后，前台该地州将显示"合作加盟"按钮</p>
                        </div>
                      )}

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="prefecture-active"
                          checked={prefectureForm.is_active}
                          onChange={(e) => setPrefectureForm(prev => ({ ...prev, is_active: e.target.checked }))}
                          className="rounded border-emerald-300 text-emerald-600 focus:ring-emerald-500"
                        />
                        <Label htmlFor="prefecture-active">启用显示</Label>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          onClick={async () => {
                            if (editingPrefecture) {
                              await handleUpdatePrefecture()
                              // 保存服务配置
                              await handleSavePrefectureConfig(editingPrefecture.prefecture_code)
                            } else {
                              await handleCreatePrefecture()
                            }
                          }}
                          disabled={saving || !prefectureForm.prefecture_name || !prefectureForm.prefecture_code}
                          className="bg-emerald-600 hover:bg-emerald-700 text-white"
                        >
                          {saving ? (
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          ) : (
                            <Save className="w-4 h-4 mr-2" />
                          )}
                          {saving ? '保存中...' : (editingPrefecture ? '更新' : '创建')}
                        </Button>
                        <Button
                          onClick={() => {
                            setShowPrefectureForm(false)
                            resetPrefectureForm()
                          }}
                          variant="outline"
                          className="border-emerald-200 text-emerald-700 hover:bg-emerald-50"
                        >
                          取消
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* 地州列表 */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {prefectureData.map((prefecture) => (
                    <Card key={prefecture.id} className="border-emerald-100">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex items-center gap-2">
                            <Circle
                              className="w-4 h-4"
                              style={{ color: prefecture.color_code, fill: prefecture.color_code }}
                            />
                            <h4 className="font-semibold text-gray-900">{prefecture.prefecture_name}</h4>
                          </div>
                          <div className="flex gap-1">
                            <Button
                              onClick={() => handleEditPrefecture(prefecture)}
                              size="sm"
                              variant="outline"
                              className="border-emerald-200 text-emerald-700 hover:bg-emerald-50 p-1"
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                            <Button
                              onClick={() => handleDeletePrefecture(prefecture.id)}
                              size="sm"
                              variant="outline"
                              className="border-red-200 text-red-700 hover:bg-red-50 p-1"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="text-sm text-gray-600">
                            代码: {prefecture.prefecture_code}
                          </div>
                          <div className="text-sm">
                            <span className="text-gray-600">状态: </span>
                            <select
                              value={prefecture.service_status}
                              onChange={(e) => handleUpdatePrefectureStatus(prefecture.id, e.target.value as ServiceStatus)}
                              disabled={saving}
                              className="text-sm border border-gray-200 rounded px-2 py-1 focus:border-emerald-500 focus:outline-none"
                              style={{ color: prefecture.color_code }}
                            >
                              <option value="未开通">未开通</option>
                              <option value="部分开通">部分开通</option>
                              <option value="已开通">已开通</option>
                            </select>
                          </div>
                          {prefecture.description && (
                            <div className="text-xs text-gray-500 line-clamp-2">
                              {prefecture.description}
                            </div>
                          )}
                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <span>排序: {prefecture.sort_order}</span>
                            <span className={`px-2 py-1 rounded ${prefecture.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                              {prefecture.is_active ? '已启用' : '已禁用'}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
