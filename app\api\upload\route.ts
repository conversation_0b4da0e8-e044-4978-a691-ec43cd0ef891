import { NextRequest, NextResponse } from 'next/server'
import { writeFile } from 'fs/promises'
import path from 'path'
import { saveImageToDatabase, updateSettingImage, isValidImageType } from '@/lib/upload'
import { ApiResponse } from '@/lib/types/database'

// POST - 上传图片
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const usageType = formData.get('usageType') as string
    const settingKey = formData.get('settingKey') as string

    if (!file) {
      const response: ApiResponse = {
        success: false,
        error: '没有选择文件'
      }
      return NextResponse.json(response, { status: 400 })
    }

    // 验证文件类型
    if (!isValidImageType(file.type)) {
      const response: ApiResponse = {
        success: false,
        error: '不支持的文件类型，请上传图片文件'
      }
      return NextResponse.json(response, { status: 400 })
    }

    // 验证文件大小 (10MB)
    if (file.size > 10 * 1024 * 1024) {
      const response: ApiResponse = {
        success: false,
        error: '文件大小不能超过10MB'
      }
      return NextResponse.json(response, { status: 400 })
    }

    // 生成唯一文件名
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    const ext = path.extname(file.name)
    const filename = `${usageType || 'image'}-${uniqueSuffix}${ext}`
    
    // 确保上传目录存在
    const uploadDir = path.join(process.cwd(), 'public/uploads')
    const filePath = path.join(uploadDir, filename)
    
    // 保存文件
    await writeFile(filePath, buffer)
    
    // 生成访问URL
    const fileUrl = `/uploads/${filename}`
    
    // 保存到数据库
    const imageId = await saveImageToDatabase({
      filename,
      originalName: file.name,
      filePath: fileUrl,
      fileSize: file.size,
      mimeType: file.type,
      usageType: usageType || undefined,
      altText: `${usageType || '图片'} - ${file.name}`
    })
    
    // 如果指定了设置键，更新对应的设置
    if (settingKey) {
      await updateSettingImage(settingKey, fileUrl)
    }
    
    const response: ApiResponse<{ 
      id: number
      filename: string
      url: string
      size: number
    }> = {
      success: true,
      data: {
        id: imageId,
        filename,
        url: fileUrl,
        size: file.size
      },
      message: '图片上传成功'
    }
    
    return NextResponse.json(response)
    
  } catch (error) {
    console.error('图片上传失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '图片上传失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// GET - 获取图片列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const usageType = searchParams.get('usageType')
    const limit = parseInt(searchParams.get('limit') || '20')
    
    // 这里可以添加获取图片列表的逻辑
    // 暂时返回空列表
    const response: ApiResponse<any[]> = {
      success: true,
      data: []
    }
    
    return NextResponse.json(response)
    
  } catch (error) {
    console.error('获取图片列表失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '获取图片列表失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
