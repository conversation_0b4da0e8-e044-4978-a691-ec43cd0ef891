import { NextRequest, NextResponse } from 'next/server'
import { writeFileSync } from 'fs'
import path from 'path'
import crypto from 'crypto'

// POST - 创建环境配置文件
export async function POST(request: NextRequest) {
  try {
    const { database, site } = await request.json()
    
    // 验证必填字段
    if (!database || !site) {
      return NextResponse.json({
        success: false,
        error: '缺少必要的配置信息'
      }, { status: 400 })
    }

    // 生成随机密钥
    const nextAuthSecret = crypto.randomBytes(32).toString('hex')
    const jwtSecret = crypto.randomBytes(32).toString('hex')
    
    // 构建环境配置内容
    const envContent = `# 数据库配置
DB_HOST=${database.host}
DB_PORT=${database.port}
DB_USER=${database.user}
DB_PASSWORD=${database.password}
DB_NAME=${database.database}

# 数据库连接URL
DATABASE_URL=mysql://${database.user}:${database.password}@${database.host}:${database.port}/${database.database}

# Next.js配置
NEXTAUTH_SECRET=${nextAuthSecret}
NEXTAUTH_URL=${site.siteUrl}

# JWT密钥
JWT_SECRET=${jwtSecret}

# 文件上传配置
UPLOAD_DIR=./public/uploads
MAX_FILE_SIZE=10485760

# 网站配置
SITE_NAME=${site.siteName}
SITE_URL=${site.siteUrl}
ADMIN_EMAIL=${site.adminEmail}

# 管理员配置
ADMIN_DEFAULT_PASSWORD=admin123456

# 安装时间
INSTALL_TIME=${new Date().toISOString()}
`

    // 写入环境配置文件
    const envPath = path.join(process.cwd(), '.env.local')
    writeFileSync(envPath, envContent, 'utf8')
    
    return NextResponse.json({
      success: true,
      message: '环境配置文件创建成功',
      envPath
    })
    
  } catch (error) {
    console.error('创建环境配置文件失败:', error)
    return NextResponse.json({
      success: false,
      error: '创建环境配置文件失败'
    }, { status: 500 })
  }
}
