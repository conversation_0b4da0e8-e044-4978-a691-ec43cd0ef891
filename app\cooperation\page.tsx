"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import {
  Heart,
  ArrowLeft,
  Phone,
  Mail,
  MapPin,
  Handshake,
  Send,
  CheckCircle,
  TrendingUp,
  Users,
  Globe,
  Award,
  ArrowRight,
  Loader2,
} from "lucide-react"
import { cooperationApi } from "@/lib/api-client"

export default function CooperationPage() {
  // 网站设置状态
  const [siteSettings, setSiteSettings] = useState({
    site_name: '滇护通',
    site_logo: '/placeholder.svg?height=60&width=200',
    phone: '************',
    address: '云南省昆明市五华区春城路100号'
  })

  const [formData, setFormData] = useState({
    name: "",
    company: "",
    phone: "",
    email: "",
    region: "",
    cooperation_type: "",
    experience: "",
    message: "",
    agreed: false,
  })

  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 获取网站设置
  useEffect(() => {
    const fetchSiteSettings = async () => {
      try {
        const response = await fetch('/api/settings')
        const result = await response.json()
        if (result.success) {
          setSiteSettings({
            site_name: result.data.site_name || '滇护通',
            site_logo: result.data.site_logo || '/placeholder.svg?height=60&width=200',
            phone: result.data.phone || '************',
            address: result.data.address || '云南省昆明市五华区春城路100号'
          })
        }
      } catch (error) {
        console.error('获取网站设置失败:', error)
      }
    }

    fetchSiteSettings()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await cooperationApi.create({
        name: formData.name,
        company: formData.company,
        phone: formData.phone,
        email: formData.email,
        region: formData.region,
        cooperation_type: formData.cooperation_type,
        experience: formData.experience,
        message: formData.message,
      })

      if (response.success) {
        setIsSubmitted(true)
      } else {
        alert("提交失败：" + response.error)
      }
    } catch (error) {
      console.error('提交合作申请失败:', error)
      alert("提交失败，请重试")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50 flex items-center justify-center px-4">
        <Card className="max-w-md w-full text-center border-0 shadow-2xl">
          <CardContent className="p-8">
            <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">提交成功！</h2>
            <p className="text-gray-600 mb-6">
              感谢您的合作意向，我们的商务团队将在24小时内与您联系，共同探讨合作机会。
            </p>
            <Button
              onClick={() => (window.location.href = "/")}
              className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700"
            >
              返回首页
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50">
      {/* Header */}
      <header className="border-b border-emerald-100 bg-white/90 backdrop-blur-md sticky top-0 z-50 shadow-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <img
              src={siteSettings.site_logo}
              alt={`${siteSettings.site_name} Logo`}
              className="h-10 w-auto object-contain"
              onError={(e) => {
                // 如果logo加载失败，显示默认图标
                e.currentTarget.style.display = 'none'
                e.currentTarget.nextElementSibling?.classList.remove('hidden')
              }}
            />
            <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg hidden">
              <Heart className="w-6 h-6 text-white" />
            </div>
            <span className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
              {siteSettings.site_name}
            </span>
          </div>
          <Button variant="outline" className="border-emerald-200 text-emerald-700 hover:bg-emerald-50 bg-transparent">
            <ArrowLeft className="w-4 h-4 mr-2" />
            <a href="/">返回首页</a>
          </Button>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-100/50 via-transparent to-teal-100/50"></div>
        <div className="container mx-auto text-center max-w-5xl relative z-10">
          <Badge className="mb-6 bg-gradient-to-r from-emerald-100 to-teal-100 text-emerald-700 hover:from-emerald-200 hover:to-teal-200 transition-all duration-300 px-4 py-2 text-sm font-medium border-0">
            <Handshake className="w-4 h-4 mr-2" />
            合作共赢计划
          </Badge>
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-8 leading-tight">
            携手共创医疗陪护
            <span className="bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">新生态</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 mb-10 leading-relaxed max-w-3xl mx-auto">
            共享千亿级市场机遇，与{siteSettings.site_name}一起开创医疗陪护服务的美好未来
          </p>
        </div>
      </section>

      {/* Market Overview */}
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">市场前景广阔</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">中国医疗陪护服务市场正迎来黄金发展期</p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-8">行业发展趋势</h3>
              <div className="space-y-8">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center flex-shrink-0">
                    <TrendingUp className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900 mb-3">市场规模持续增长</h4>
                    <p className="text-gray-600 leading-relaxed">
                      中国医疗陪护服务市场预计2025年将达到500亿元，年复合增长率超过25%，市场潜力巨大
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900 mb-3">用户需求旺盛</h4>
                    <p className="text-gray-600 leading-relaxed">
                      老龄化社会加速到来，家庭结构变化，专业陪护需求急剧增长，市场供不应求
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Globe className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900 mb-3">政策大力支持</h4>
                    <p className="text-gray-600 leading-relaxed">
                      国家大力支持健康服务业发展，多项政策为行业发展提供有力保障
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-3xl p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">市场数据</h3>
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-emerald-600 mb-2">500亿</div>
                  <div className="text-gray-600">预计市场规模</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-emerald-600 mb-2">25%</div>
                  <div className="text-gray-600">年复合增长率</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-emerald-600 mb-2">2.5亿</div>
                  <div className="text-gray-600">老年人口数量</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-emerald-600 mb-2">80%</div>
                  <div className="text-gray-600">市场空白率</div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-emerald-600 to-teal-600 rounded-3xl p-8 text-white text-center">
            <h3 className="text-2xl font-bold mb-4">抓住机遇，共创未来</h3>
            <p className="text-emerald-100 text-lg max-w-3xl mx-auto">
              医疗陪护服务正处于快速发展的黄金期，现在加入{siteSettings.site_name}，与我们一起抢占市场先机，共享行业发展红利
            </p>
          </div>
        </div>
      </section>

      {/* Cooperation Advantages */}
      <section className="py-20 px-4 bg-gradient-to-r from-gray-50 to-emerald-50/30">
        <div className="container mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">合作优势</h2>
            <p className="text-xl text-gray-600">选择{siteSettings.site_name}，享受全方位合作支持</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "成熟的平台技术",
                description: "完善的技术架构和运营体系，为合作伙伴提供稳定可靠的平台支持",
                icon: "🔧",
              },
              {
                title: "完善的服务标准",
                description: "标准化的服务流程和质量保障体系，确保服务品质的一致性",
                icon: "⭐",
              },
              {
                title: "专业的培训认证",
                description: "系统的培训课程和认证体系，帮助合作伙伴快速提升专业能力",
                icon: "🎓",
              },
              {
                title: "强大的品牌影响力",
                description: "知名品牌和良好口碑，为合作伙伴带来更多用户信任和市场机会",
                icon: "🏆",
              },
              {
                title: "全方位市场推广",
                description: "线上线下全渠道营销支持，助力合作伙伴快速拓展市场",
                icon: "📢",
              },
              {
                title: "灵活的收益分成",
                description: "合理的收益分配机制，让合作伙伴获得丰厚的投资回报",
                icon: "💰",
              },
            ].map((advantage, index) => (
              <Card
                key={index}
                className="text-center hover:shadow-xl transition-all duration-300 transform hover:scale-105 border-0 bg-white/80 backdrop-blur-sm"
              >
                <CardContent className="p-6">
                  <div className="text-4xl mb-4">{advantage.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{advantage.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{advantage.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Cooperation Models */}
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">合作模式</h2>
            <p className="text-xl text-gray-600">多种合作方式，总有一种适合您</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <Card className="text-center hover:shadow-xl transition-all duration-300 transform hover:scale-105 border-0 bg-gradient-to-br from-white to-emerald-50/30 relative overflow-hidden">
              <div className="absolute top-0 right-0 bg-gradient-to-br from-emerald-500 to-teal-600 text-white px-3 py-1 text-sm font-medium rounded-bl-lg">
                推荐
              </div>
              <CardHeader>
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Handshake className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-2xl">区域代理</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  成为区域独家代理商，享受区域保护政策，共享平台发展红利
                </p>
                <div className="space-y-3 mb-6">
                  <div className="flex items-center gap-2 text-sm text-gray-700">
                    <CheckCircle className="w-4 h-4 text-emerald-600" />
                    区域独家经营权
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-700">
                    <CheckCircle className="w-4 h-4 text-emerald-600" />
                    品牌授权使用
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-700">
                    <CheckCircle className="w-4 h-4 text-emerald-600" />
                    技术平台支持
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-700">
                    <CheckCircle className="w-4 h-4 text-emerald-600" />
                    运营指导培训
                  </div>
                </div>
                <div className="text-2xl font-bold text-emerald-600 mb-2">面议</div>
                <div className="text-sm text-gray-500">投资门槛</div>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-xl transition-all duration-300 transform hover:scale-105 border-0 bg-gradient-to-br from-white to-emerald-50/30">
              <CardHeader>
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-2xl">服务商合作</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-6 leading-relaxed">整合本地医疗资源，提供专业陪护服务，实现互利共赢</p>
                <div className="space-y-3 mb-6">
                  <div className="flex items-center gap-2 text-sm text-gray-700">
                    <CheckCircle className="w-4 h-4 text-emerald-600" />
                    订单分配机制
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-700">
                    <CheckCircle className="w-4 h-4 text-emerald-600" />
                    服务质量监控
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-700">
                    <CheckCircle className="w-4 h-4 text-emerald-600" />
                    收益分成模式
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-700">
                    <CheckCircle className="w-4 h-4 text-emerald-600" />
                    专业技能培训
                  </div>
                </div>
                <div className="text-2xl font-bold text-emerald-600 mb-2">面议</div>
                <div className="text-sm text-gray-500">投资门槛</div>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-xl transition-all duration-300 transform hover:scale-105 border-0 bg-gradient-to-br from-white to-emerald-50/30">
              <CardHeader>
                <div className="w-16 h-16 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Award className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-2xl">医院合作</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-6 leading-relaxed">与医疗机构深度合作，规范服务流程，提升陪护水平，为患者提供一站式陪护服务</p>
                <div className="space-y-3 mb-6">
                  <div className="flex items-center gap-2 text-sm text-gray-700">
                    <CheckCircle className="w-4 h-4 text-emerald-600" />
                    院内服务对接
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-700">
                    <CheckCircle className="w-4 h-4 text-emerald-600" />
                    患者资源共享
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-700">
                    <CheckCircle className="w-4 h-4 text-emerald-600" />
                    服务标准统一
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-700">
                    <CheckCircle className="w-4 h-4 text-emerald-600" />
                    品牌联合推广
                  </div>
                </div>
                <div className="text-2xl font-bold text-emerald-600 mb-2">面议</div>
                <div className="text-sm text-gray-500">投资门槛</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Success Cases */}
      <section className="py-20 px-4 bg-gradient-to-r from-emerald-50 to-teal-50">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">成功案例</h2>
            <p className="text-xl text-gray-600">看看我们的合作伙伴如何获得成功</p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card className="bg-white hover:shadow-xl transition-all duration-300 border-0">
              <CardContent className="p-8">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                    张
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">张总 - 昆明区域代理</h3>
                    <p className="text-gray-600">加入{siteSettings.site_name}6个月</p>
                  </div>
                </div>
                <p className="text-gray-600 leading-relaxed mb-4">
                  "加入{siteSettings.site_name}后，我们的业务快速发展。平台提供的技术支持和运营指导非常专业，
                  帮助我们在短短6个月内建立了稳定的客户群体，月收入已经超过20万元。"
                </p>
                <div className="flex items-center gap-4 text-sm text-emerald-600">
                  <span>月收入: 20万+</span>
                  <span>服务用户: 500+</span>
                  <span>满意度: 98%</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white hover:shadow-xl transition-all duration-300 border-0">
              <CardContent className="p-8">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                    李
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">李女士 - 服务商合作</h3>
                    <p className="text-gray-600">加入{siteSettings.site_name}1年</p>
                  </div>
                </div>
                <p className="text-gray-600 leading-relaxed mb-4">
                  "作为护理专业出身，我一直想创业。{siteSettings.site_name}的服务商合作模式让我能够充分发挥专业优势，
                  现在我的团队已经发展到15人，业务覆盖整个大理地区。"
                </p>
                <div className="flex items-center gap-4 text-sm text-emerald-600">
                  <span>团队规模: 15人</span>
                  <span>月订单: 300+</span>
                  <span>覆盖区域: 大理全市</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">立即咨询合作</h2>
            <p className="text-xl text-gray-600">填写以下信息，我们将在24小时内与您联系</p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* 联系信息 */}
            <div className="lg:col-span-1">
              <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-emerald-50/30 sticky top-24">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-emerald-700">
                    <Handshake className="w-6 h-6" />
                    联系我们
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Phone className="w-4 h-4 text-emerald-600" />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">商务热线</div>
                      <div className="text-gray-600">{siteSettings.phone}</div>
                      <div className="text-sm text-gray-500">工作日 9:00-18:00</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Mail className="w-4 h-4 text-emerald-600" />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">商务邮箱</div>
                      <div className="text-gray-600"><EMAIL></div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <MapPin className="w-4 h-4 text-emerald-600" />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">公司地址</div>
                      <div className="text-gray-600">{siteSettings.address}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 合作表单 */}
            <div className="lg:col-span-2">
              <Card className="border-0 shadow-xl">
                <CardHeader>
                  <CardTitle className="text-2xl text-gray-900">提交合作意向</CardTitle>
                  <p className="text-gray-600">请填写以下信息，我们将尽快与您联系</p>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="name">姓名 *</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                          placeholder="请输入您的姓名"
                          required
                          className="border-emerald-200 focus:border-emerald-500"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="company">公司名称</Label>
                        <Input
                          id="company"
                          value={formData.company}
                          onChange={(e) => handleInputChange("company", e.target.value)}
                          placeholder="请输入公司名称"
                          className="border-emerald-200 focus:border-emerald-500"
                        />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="phone">联系电话 *</Label>
                        <Input
                          id="phone"
                          type="tel"
                          value={formData.phone}
                          onChange={(e) => handleInputChange("phone", e.target.value)}
                          placeholder="请输入手机号码"
                          required
                          className="border-emerald-200 focus:border-emerald-500"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">邮箱地址</Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange("email", e.target.value)}
                          placeholder="请输入邮箱地址"
                          className="border-emerald-200 focus:border-emerald-500"
                        />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="region">意向合作区域 *</Label>
                        <Select onValueChange={(value) => handleInputChange("region", value)}>
                          <SelectTrigger className="border-emerald-200 focus:border-emerald-500">
                            <SelectValue placeholder="请选择区域" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="kunming">昆明市</SelectItem>
                            <SelectItem value="dali">大理市</SelectItem>
                            <SelectItem value="lijiang">丽江市</SelectItem>
                            <SelectItem value="qujing">曲靖市</SelectItem>
                            <SelectItem value="yuxi">玉溪市</SelectItem>
                            <SelectItem value="other">其他地区</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="cooperationType">合作类型 *</Label>
                        <Select onValueChange={(value) => handleInputChange("cooperation_type", value)}>
                          <SelectTrigger className="border-emerald-200 focus:border-emerald-500">
                            <SelectValue placeholder="请选择合作类型" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="agent">区域代理</SelectItem>
                            <SelectItem value="service">服务商合作</SelectItem>
                            <SelectItem value="hospital">医院合作</SelectItem>
                            <SelectItem value="other">其他合作</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="experience">相关经验</Label>
                      <Textarea
                        id="experience"
                        value={formData.experience}
                        onChange={(e) => handleInputChange("experience", e.target.value)}
                        placeholder="请简述您在医疗服务或相关行业的经验"
                        className="border-emerald-200 focus:border-emerald-500 min-h-[100px]"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message">合作意向说明 *</Label>
                      <Textarea
                        id="message"
                        value={formData.message}
                        onChange={(e) => handleInputChange("message", e.target.value)}
                        placeholder="请详细描述您的合作意向、预期投资规模、团队情况等"
                        required
                        className="border-emerald-200 focus:border-emerald-500 min-h-[120px]"
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="agreement"
                        checked={formData.agreed}
                        onCheckedChange={(checked) => handleInputChange("agreed", checked as boolean)}
                        className="border-emerald-300 data-[state=checked]:bg-emerald-600"
                      />
                      <Label htmlFor="agreement" className="text-sm text-gray-600">
                        我已阅读并同意《合作协议条款》和《隐私政策》
                      </Label>
                    </div>

                    <Button
                      type="submit"
                      disabled={
                        isSubmitting ||
                        !formData.agreed ||
                        !formData.name ||
                        !formData.phone ||
                        !formData.region ||
                        !formData.cooperation_type ||
                        !formData.message
                      }
                      className="w-full bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white py-3 text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 group disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                      ) : (
                        <Send className="w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform duration-300" />
                      )}
                      {isSubmitting ? "提交中..." : "提交合作意向"}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-br from-emerald-600 via-teal-600 to-cyan-600 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-600/90 to-teal-600/90"></div>
        <div className="container mx-auto text-center max-w-5xl relative z-10">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">加入滇护通，共创美好未来</h2>
          <p className="text-xl md:text-2xl mb-10 text-emerald-100 leading-relaxed max-w-4xl mx-auto">
            医疗陪护服务市场正迎来黄金发展期，现在就是最好的时机。 与滇护通携手，共同开创医疗陪护服务的新篇章！
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Button
              size="lg"
              className="bg-white text-emerald-600 hover:bg-gray-100 hover:text-emerald-700 text-lg px-10 py-4 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 group"
            >
              <Phone className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-300" />
              立即咨询合作
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-2 border-white text-white hover:bg-white hover:text-emerald-600 text-lg px-10 py-4 bg-transparent transition-all duration-300 transform hover:scale-105"
            >
              下载合作手册
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
