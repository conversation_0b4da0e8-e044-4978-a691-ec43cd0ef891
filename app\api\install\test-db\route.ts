import { NextRequest, NextResponse } from 'next/server'
import mysql from 'mysql2/promise'

// POST - 测试数据库连接
export async function POST(request: NextRequest) {
  try {
    const { host, port, user, password, database } = await request.json()
    
    // 验证必填字段
    if (!host || !port || !user || !database) {
      return NextResponse.json({
        success: false,
        error: '请填写完整的数据库连接信息'
      }, { status: 400 })
    }

    // 首先测试连接到MySQL服务器（不指定数据库）
    let connection
    try {
      connection = await mysql.createConnection({
        host,
        port: parseInt(port),
        user,
        password: password || undefined,
        connectTimeout: 10000,
        acquireTimeout: 10000,
        timeout: 10000,
      })
      
      // 测试连接
      await connection.ping()
      
      // 检查数据库是否存在
      const [databases] = await connection.execute(
        'SHOW DATABASES LIKE ?',
        [database]
      )
      
      let databaseExists = (databases as any[]).length > 0
      
      // 如果数据库不存在，尝试创建
      if (!databaseExists) {
        try {
          await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${database}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`)
          databaseExists = true
        } catch (createError) {
          console.error('创建数据库失败:', createError)
          return NextResponse.json({
            success: false,
            error: `数据库 "${database}" 不存在且无法创建。请手动创建数据库或检查权限。`
          }, { status: 400 })
        }
      }
      
      // 测试连接到指定数据库
      await connection.changeUser({ database })
      
      return NextResponse.json({
        success: true,
        message: '数据库连接成功',
        databaseExists,
        info: {
          host,
          port,
          database,
          user
        }
      })
      
    } finally {
      if (connection) {
        await connection.end()
      }
    }
    
  } catch (error: any) {
    console.error('数据库连接测试失败:', error)
    
    let errorMessage = '数据库连接失败'
    
    if (error.code === 'ECONNREFUSED') {
      errorMessage = '无法连接到数据库服务器，请检查主机和端口是否正确'
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      errorMessage = '数据库用户名或密码错误'
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      errorMessage = '指定的数据库不存在'
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = '数据库连接超时'
    } else if (error.message) {
      errorMessage = error.message
    }
    
    return NextResponse.json({
      success: false,
      error: errorMessage,
      code: error.code
    }, { status: 400 })
  }
}
