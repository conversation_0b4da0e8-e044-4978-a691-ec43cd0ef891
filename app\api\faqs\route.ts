import { NextRequest, NextResponse } from 'next/server'
import { executeQuery, executeInsert, executeUpdate } from '@/lib/database'
import { FAQ, ApiResponse, FAQFormData, PaginatedResponse } from '@/lib/types/database'

// GET - 获取FAQ列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const category = searchParams.get('category')
    const active_only = searchParams.get('active_only') === 'true'
    const offset = (page - 1) * limit
    
    // 构建查询条件
    let whereClause = ''
    let queryParams: any[] = []
    
    const conditions = []
    if (category && category !== 'all') {
      conditions.push('category = ?')
      queryParams.push(category)
    }
    if (active_only) {
      conditions.push('is_active = TRUE')
    }
    
    if (conditions.length > 0) {
      whereClause = 'WHERE ' + conditions.join(' AND ')
    }
    
    // 获取总数
    const totalQuery = `SELECT COUNT(*) as total FROM faqs ${whereClause}`
    const totalResult = await executeQuery<{ total: number }>(totalQuery, queryParams)
    const total = totalResult[0].total
    
    // 获取数据
    const dataQuery = `
      SELECT * FROM faqs 
      ${whereClause}
      ORDER BY sort_order ASC, created_at DESC 
      LIMIT ? OFFSET ?
    `
    const faqs = await executeQuery<FAQ>(
      dataQuery, 
      [...queryParams, limit, offset]
    )
    
    const response: ApiResponse<PaginatedResponse<FAQ>> = {
      success: true,
      data: {
        data: faqs,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('获取FAQ列表失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '获取FAQ列表失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// POST - 创建新的FAQ
export async function POST(request: NextRequest) {
  try {
    const body: FAQFormData = await request.json()
    
    // 验证必填字段
    if (!body.question || !body.answer) {
      const response: ApiResponse = {
        success: false,
        error: '问题和答案为必填字段'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 插入数据
    const insertId = await executeInsert(
      `INSERT INTO faqs 
       (question, answer, category, sort_order, is_active, created_by) 
       VALUES (?, ?, ?, ?, ?, ?)`,
      [
        body.question,
        body.answer,
        body.category || 'general',
        body.sort_order || 0,
        body.is_active !== undefined ? body.is_active : true,
        'admin' // 暂时硬编码，后续可以从session获取
      ]
    )
    
    const response: ApiResponse<{ id: number }> = {
      success: true,
      data: { id: insertId },
      message: 'FAQ创建成功'
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('创建FAQ失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '创建FAQ失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
