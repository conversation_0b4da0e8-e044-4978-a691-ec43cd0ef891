// 测试数据库连接脚本
const mysql = require('mysql2/promise')

const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: '4MndSrzT8TSB7kPz',
  database: 'dianfuto',
  charset: 'utf8mb4'
}

async function testDatabase() {
  let connection
  
  try {
    console.log('正在连接数据库...')
    connection = await mysql.createConnection(dbConfig)
    
    console.log('数据库连接成功！')
    
    // 测试查询
    console.log('测试查询网站设置...')
    const [settings] = await connection.execute('SELECT * FROM site_settings LIMIT 3')
    console.log('网站设置数据:', settings)
    
    console.log('测试查询合作申请...')
    const [applications] = await connection.execute('SELECT * FROM cooperation_applications LIMIT 2')
    console.log('合作申请数据:', applications)
    
    console.log('数据库测试完成！')
    
  } catch (error) {
    console.error('数据库测试失败:', error.message)
  } finally {
    if (connection) {
      await connection.end()
      console.log('数据库连接已关闭')
    }
  }
}

testDatabase()
