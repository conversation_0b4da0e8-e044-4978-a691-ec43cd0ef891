import multer from 'multer'
import path from 'path'
import fs from 'fs'
import { executeInsert, executeUpdate } from './database'

// 确保上传目录存在
const uploadDir = path.join(process.cwd(), 'public/uploads')
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true })
}

// 配置multer存储
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir)
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    const ext = path.extname(file.originalname)
    const filename = `${file.fieldname}-${uniqueSuffix}${ext}`
    cb(null, filename)
  }
})

// 文件过滤器
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // 只允许图片文件
  if (file.mimetype.startsWith('image/')) {
    cb(null, true)
  } else {
    cb(new Error('只允许上传图片文件'))
  }
}

// 创建multer实例
export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB限制
  }
})

// 图片信息接口
export interface ImageInfo {
  id?: number
  filename: string
  originalName: string
  filePath: string
  fileSize: number
  mimeType: string
  width?: number
  height?: number
  usageType?: string
  altText?: string
}

// 保存图片信息到数据库
export async function saveImageToDatabase(imageInfo: ImageInfo): Promise<number> {
  const insertId = await executeInsert(
    `INSERT INTO images 
     (filename, original_name, file_path, file_size, mime_type, width, height, usage_type, alt_text, uploaded_by) 
     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
    [
      imageInfo.filename,
      imageInfo.originalName,
      imageInfo.filePath,
      imageInfo.fileSize,
      imageInfo.mimeType,
      imageInfo.width || null,
      imageInfo.height || null,
      imageInfo.usageType || null,
      imageInfo.altText || null,
      'admin' // 暂时硬编码，后续可以从session获取
    ]
  )
  return insertId
}

// 更新网站设置中的图片路径
export async function updateSettingImage(settingKey: string, imagePath: string): Promise<void> {
  await executeUpdate(
    'UPDATE settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?',
    [imagePath, settingKey]
  )
}

// 获取图片尺寸（需要安装sharp库来获取图片信息）
export function getImageDimensions(filePath: string): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    // 这里可以使用sharp库来获取图片尺寸
    // 暂时返回默认值
    resolve({ width: 0, height: 0 })
  })
}

// 删除文件
export function deleteFile(filePath: string): Promise<void> {
  return new Promise((resolve, reject) => {
    fs.unlink(filePath, (err) => {
      if (err && err.code !== 'ENOENT') {
        reject(err)
      } else {
        resolve()
      }
    })
  })
}

// 验证文件类型
export function isValidImageType(mimeType: string): boolean {
  const allowedTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
  ]
  return allowedTypes.includes(mimeType)
}

// 生成缩略图路径
export function generateThumbnailPath(originalPath: string): string {
  const ext = path.extname(originalPath)
  const name = path.basename(originalPath, ext)
  const dir = path.dirname(originalPath)
  return path.join(dir, `${name}_thumb${ext}`)
}
