// 检查和创建必要数据库表的脚本
const mysql = require('mysql2/promise')
require('dotenv').config({ path: '.env.local' })

async function checkAndCreateTables() {
  console.log('🔍 开始检查数据库表...')
  console.log('数据库配置:', {
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    database: process.env.DB_NAME,
    port: process.env.DB_PORT
  })

  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '4MndSrzT8TSB7kPz',
    database: process.env.DB_NAME || 'dianfuto',
    port: parseInt(process.env.DB_PORT || '3306'),
  })

  try {
    console.log('✅ 数据库连接成功')

    // 检查images表是否存在
    console.log('📝 检查images表...')
    const [imagesTables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'dianfuto' 
      AND TABLE_NAME = 'images'
    `)

    if (imagesTables.length === 0) {
      console.log('📝 创建images表...')
      await connection.execute(`
        CREATE TABLE images (
          id INT AUTO_INCREMENT PRIMARY KEY,
          filename VARCHAR(255) NOT NULL,
          original_name VARCHAR(255) NOT NULL,
          file_path VARCHAR(500) NOT NULL,
          file_size INT NOT NULL,
          mime_type VARCHAR(100) NOT NULL,
          width INT DEFAULT NULL,
          height INT DEFAULT NULL,
          usage_type VARCHAR(50) DEFAULT NULL,
          alt_text TEXT DEFAULT NULL,
          uploaded_by VARCHAR(50) DEFAULT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_usage_type (usage_type),
          INDEX idx_uploaded_by (uploaded_by),
          INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `)
      console.log('✅ images表创建成功')
    } else {
      console.log('ℹ️  images表已存在')
    }

    // 检查settings表是否存在
    console.log('📝 检查settings表...')
    const [settingsTables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'dianfuto' 
      AND TABLE_NAME = 'settings'
    `)

    if (settingsTables.length === 0) {
      console.log('📝 创建settings表...')
      await connection.execute(`
        CREATE TABLE settings (
          id INT AUTO_INCREMENT PRIMARY KEY,
          setting_key VARCHAR(100) NOT NULL UNIQUE,
          setting_value TEXT DEFAULT NULL,
          description TEXT DEFAULT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_setting_key (setting_key)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `)
      console.log('✅ settings表创建成功')
    } else {
      console.log('ℹ️  settings表已存在')
    }

    // 检查miniprogram_qrcode设置项
    console.log('📝 检查小程序码设置项...')
    const [qrcodeSetting] = await connection.execute(
      'SELECT * FROM settings WHERE setting_key = ?',
      ['miniprogram_qrcode']
    )

    if (qrcodeSetting.length === 0) {
      console.log('📝 插入小程序码设置项...')
      await connection.execute(`
        INSERT INTO settings (setting_key, setting_value, description) 
        VALUES ('miniprogram_qrcode', '', '小程序码图片路径')
      `)
      console.log('✅ 小程序码设置项插入成功')
    } else {
      console.log('ℹ️  小程序码设置项已存在')
    }

    // 检查其他必要的设置项
    const requiredSettings = [
      { key: 'site_name', value: '滇护通', description: '网站名称' },
      { key: 'hero_title', value: '让医疗陪护更简单、更安心', description: '首页标题' },
      { key: 'hero_subtitle', value: '足不出户，为家人预约专业的陪诊、医疗跑腿、代办、上门护工等服务', description: '首页副标题' },
      { key: 'hero_image', value: '', description: '首页头图' },
      { key: 'site_logo', value: '', description: '网站Logo' }
    ]

    console.log('📝 检查其他设置项...')
    for (const setting of requiredSettings) {
      const [existingSetting] = await connection.execute(
        'SELECT * FROM settings WHERE setting_key = ?',
        [setting.key]
      )

      if (existingSetting.length === 0) {
        await connection.execute(`
          INSERT INTO settings (setting_key, setting_value, description) 
          VALUES (?, ?, ?)
        `, [setting.key, setting.value, setting.description])
        console.log(`✅ 设置项 ${setting.key} 插入成功`)
      }
    }

    console.log('🎉 数据库表检查和创建完成！')

    // 测试上传目录
    const fs = require('fs')
    const path = require('path')
    const uploadDir = path.join(process.cwd(), 'public/uploads')
    
    console.log('📁 检查上传目录...')
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true })
      console.log('✅ 上传目录创建成功')
    } else {
      console.log('ℹ️  上传目录已存在')
    }

    // 检查目录权限
    try {
      fs.accessSync(uploadDir, fs.constants.W_OK)
      console.log('✅ 上传目录可写')
    } catch (error) {
      console.log('❌ 上传目录不可写，请检查权限')
    }

  } catch (error) {
    console.error('❌ 数据库操作失败:', error)
    process.exit(1)
  } finally {
    await connection.end()
  }
}

// 运行检查
checkAndCreateTables().catch(console.error)
