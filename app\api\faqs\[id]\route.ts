import { NextRequest, NextResponse } from 'next/server'
import { executeQuery, executeUpdate } from '@/lib/database'
import { FAQ, ApiResponse, FAQFormData } from '@/lib/types/database'

// GET - 获取单个FAQ详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      const response: ApiResponse = {
        success: false,
        error: '无效的ID'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const faq = await executeQuery<FAQ>(
      'SELECT * FROM faqs WHERE id = ?',
      [id]
    )
    
    if (faq.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: 'FAQ不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    // 增加查看次数
    await executeUpdate(
      'UPDATE faqs SET view_count = view_count + 1 WHERE id = ?',
      [id]
    )
    
    const response: ApiResponse<FAQ> = {
      success: true,
      data: faq[0]
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('获取FAQ详情失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '获取FAQ详情失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// PUT - 更新FAQ
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    const body: FAQFormData = await request.json()
    
    if (isNaN(id)) {
      const response: ApiResponse = {
        success: false,
        error: '无效的ID'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    if (!body.question || !body.answer) {
      const response: ApiResponse = {
        success: false,
        error: '问题和答案为必填字段'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 更新FAQ
    const affectedRows = await executeUpdate(
      `UPDATE faqs SET 
       question = ?, answer = ?, category = ?, sort_order = ?, is_active = ?, updated_at = NOW() 
       WHERE id = ?`,
      [
        body.question,
        body.answer,
        body.category || 'general',
        body.sort_order || 0,
        body.is_active !== undefined ? body.is_active : true,
        id
      ]
    )
    
    if (affectedRows === 0) {
      const response: ApiResponse = {
        success: false,
        error: 'FAQ不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse = {
      success: true,
      message: 'FAQ更新成功'
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('更新FAQ失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '更新FAQ失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// DELETE - 删除FAQ
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      const response: ApiResponse = {
        success: false,
        error: '无效的ID'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const affectedRows = await executeUpdate(
      'DELETE FROM faqs WHERE id = ?',
      [id]
    )
    
    if (affectedRows === 0) {
      const response: ApiResponse = {
        success: false,
        error: 'FAQ不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse = {
      success: true,
      message: 'FAQ删除成功'
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('删除FAQ失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '删除FAQ失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
