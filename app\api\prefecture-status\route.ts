import { NextRequest, NextResponse } from 'next/server'
import { executeQuery, executeInsert, executeUpdate } from '@/lib/database'
import { PrefectureStatus, ApiResponse, PrefectureStatusFormData, ServiceStatus } from '@/lib/types/database'

// 获取状态对应的颜色代码 - 使用绿色系深浅区分
const getStatusColor = (status: ServiceStatus): string => {
  switch (status) {
    case '未开通':
      return '#A7F3D0'  // 浅绿色
    case '部分开通':
      return '#34D399'  // 中绿色
    case '已开通':
      return '#059669'  // 深绿色
    default:
      return '#A7F3D0'  // 默认浅绿色
  }
}

// GET - 获取地州状态列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const active_only = searchParams.get('active_only') === 'true'
    
    let whereClause = ''
    if (active_only) {
      whereClause = 'WHERE is_active = TRUE'
    }
    
    const prefectures = await executeQuery<PrefectureStatus>(
      `SELECT * FROM prefecture_status ${whereClause} ORDER BY sort_order ASC, prefecture_name ASC`
    )
    
    const response: ApiResponse<PrefectureStatus[]> = {
      success: true,
      data: prefectures
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('获取地州状态失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '获取地州状态失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// POST - 创建新的地州状态
export async function POST(request: NextRequest) {
  try {
    const body: PrefectureStatusFormData = await request.json()
    
    // 验证必填字段
    if (!body.prefecture_name || !body.prefecture_code || !body.service_status) {
      const response: ApiResponse = {
        success: false,
        error: '地州名称、代码和服务状态为必填字段'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 检查代码是否已存在
    const existing = await executeQuery<PrefectureStatus>(
      'SELECT id FROM prefecture_status WHERE prefecture_code = ?',
      [body.prefecture_code]
    )
    
    if (existing.length > 0) {
      const response: ApiResponse = {
        success: false,
        error: '地州代码已存在'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 自动设置颜色代码
    const colorCode = body.color_code || getStatusColor(body.service_status)
    
    // 插入数据
    const insertId = await executeInsert(
      `INSERT INTO prefecture_status 
       (prefecture_name, prefecture_code, service_status, color_code, description, sort_order, is_active) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        body.prefecture_name,
        body.prefecture_code,
        body.service_status,
        colorCode,
        body.description || '',
        body.sort_order || 0,
        body.is_active !== undefined ? body.is_active : true
      ]
    )
    
    const response: ApiResponse<{ id: number }> = {
      success: true,
      data: { id: insertId },
      message: '地州状态创建成功'
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('创建地州状态失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '创建地州状态失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// PUT - 批量更新地州状态
export async function PUT(request: NextRequest) {
  try {
    const body: { updates: Array<{ id: number; service_status: ServiceStatus }> } = await request.json()
    
    if (!body.updates || !Array.isArray(body.updates)) {
      const response: ApiResponse = {
        success: false,
        error: '无效的更新数据格式'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 批量更新
    const updatePromises = body.updates.map(update => {
      const colorCode = getStatusColor(update.service_status)
      return executeUpdate(
        'UPDATE prefecture_status SET service_status = ?, color_code = ?, updated_at = NOW() WHERE id = ?',
        [update.service_status, colorCode, update.id]
      )
    })
    
    await Promise.all(updatePromises)
    
    const response: ApiResponse = {
      success: true,
      message: `成功更新 ${body.updates.length} 个地州状态`
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('批量更新地州状态失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '批量更新地州状态失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
