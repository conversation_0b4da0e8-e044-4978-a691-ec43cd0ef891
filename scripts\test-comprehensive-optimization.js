// 综合优化功能测试脚本
const API_BASE = 'http://localhost:3001/api'

// 测试绿色系颜色优化
async function testGreenColorScheme() {
  console.log('\n=== 测试绿色系颜色优化 ===')
  
  try {
    console.log('1. 测试地州状态颜色映射...')
    const response = await fetch(`${API_BASE}/prefecture-status?active_only=true`)
    const result = await response.json()
    
    if (result.success) {
      console.log('✅ 地州状态API正常')
      console.log('   - 地州数量:', result.data.length)
      
      // 检查颜色是否为绿色系
      const colorMapping = {
        '未开通': '#A7F3D0',    // 浅绿色
        '部分开通': '#34D399',  // 中绿色
        '已开通': '#059669'     // 深绿色
      }
      
      console.log('\n   颜色映射检查:')
      result.data.forEach(prefecture => {
        const expectedColor = colorMapping[prefecture.service_status]
        if (prefecture.color_code === expectedColor) {
          console.log(`   ✅ ${prefecture.prefecture_name} (${prefecture.service_status}): ${prefecture.color_code}`)
        } else {
          console.log(`   ⚠️  ${prefecture.prefecture_name} (${prefecture.service_status}): 期望 ${expectedColor}, 实际 ${prefecture.color_code}`)
        }
      })
      
      return result.data
    } else {
      console.log('❌ 地州状态API失败:', result.error)
      return []
    }
  } catch (error) {
    console.error('❌ 绿色系颜色测试失败:', error.message)
    return []
  }
}

// 测试地州名称显示
async function testPrefectureNames() {
  console.log('\n=== 测试地州名称显示 ===')
  
  try {
    console.log('1. 测试前台页面地州名称...')
    const pageResponse = await fetch('http://localhost:3001/')
    
    if (pageResponse.ok) {
      console.log('✅ 前台页面加载成功')
      
      const pageContent = await pageResponse.text()
      
      // 检查是否包含地州名称相关功能
      const nameChecks = [
        { 
          name: '地州简称映射导入', 
          pattern: 'prefectureShortNames',
          description: '导入地州简称映射数据'
        },
        { 
          name: '地州中心点导入', 
          pattern: 'prefectureCenters',
          description: '导入地州中心点坐标'
        },
        { 
          name: '地州名称标签', 
          pattern: 'text.*textAnchor.*middle',
          description: '地图上的地州名称标签'
        },
        { 
          name: '名称样式设置', 
          pattern: 'fill-white.*pointer-events-none',
          description: '地州名称的样式设置'
        }
      ]
      
      console.log('\n   地州名称功能检查:')
      nameChecks.forEach(check => {
        if (pageContent.includes(check.pattern)) {
          console.log(`   ✅ ${check.name} - 已实现`)
          console.log(`      ${check.description}`)
        } else {
          console.log(`   ⚠️  ${check.name} - 可能缺失`)
          console.log(`      ${check.description}`)
        }
      })
      
    } else {
      console.log('❌ 前台页面加载失败')
    }
    
  } catch (error) {
    console.error('❌ 地州名称显示测试失败:', error.message)
  }
}

// 测试服务管理API
async function testServiceManagementAPI() {
  console.log('\n=== 测试服务管理API ===')
  
  try {
    // 1. 测试服务类型API
    console.log('1. 测试服务类型API...')
    const serviceTypesResponse = await fetch(`${API_BASE}/service-types?active_only=true`)
    const serviceTypesResult = await serviceTypesResponse.json()
    
    if (serviceTypesResult.success) {
      console.log('✅ 服务类型API正常')
      console.log('   - 服务类型数量:', serviceTypesResult.data.length)
      
      serviceTypesResult.data.forEach(serviceType => {
        console.log(`   - ${serviceType.service_name} (${serviceType.service_code}): ${serviceType.icon}`)
      })
    } else {
      console.log('❌ 服务类型API失败:', serviceTypesResult.error)
    }
    
    // 2. 测试地州服务API
    console.log('\n2. 测试地州服务API...')
    const prefectureServicesResponse = await fetch(`${API_BASE}/prefecture-services?available_only=true`)
    const prefectureServicesResult = await prefectureServicesResponse.json()
    
    if (prefectureServicesResult.success) {
      console.log('✅ 地州服务API正常')
      console.log('   - 服务记录数量:', prefectureServicesResult.data.length)
      
      // 按地州分组统计
      const servicesByPrefecture = {}
      prefectureServicesResult.data.forEach(service => {
        if (!servicesByPrefecture[service.prefecture_name]) {
          servicesByPrefecture[service.prefecture_name] = []
        }
        servicesByPrefecture[service.prefecture_name].push(service.service_name)
      })
      
      console.log('\n   各地州可用服务:')
      Object.keys(servicesByPrefecture).forEach(prefecture => {
        console.log(`   - ${prefecture}: ${servicesByPrefecture[prefecture].length} 个服务`)
      })
      
      return prefectureServicesResult.data
    } else {
      console.log('❌ 地州服务API失败:', prefectureServicesResult.error)
      return []
    }
    
  } catch (error) {
    console.error('❌ 服务管理API测试失败:', error.message)
    return []
  }
}

// 测试前台动态服务显示
async function testDynamicServiceDisplay() {
  console.log('\n=== 测试前台动态服务显示 ===')
  
  try {
    console.log('1. 测试前台服务显示功能...')
    const pageResponse = await fetch('http://localhost:3001/')
    
    if (pageResponse.ok) {
      console.log('✅ 前台页面加载成功')
      
      const pageContent = await pageResponse.text()
      
      // 检查动态服务显示功能
      const serviceDisplayChecks = [
        { 
          name: '服务数据状态管理', 
          pattern: 'prefectureServices.*useState',
          description: '前台页面包含服务数据状态管理'
        },
        { 
          name: '服务数据获取函数', 
          pattern: 'fetchPrefectureServices',
          description: '包含获取地州服务数据的函数'
        },
        { 
          name: '动态服务显示', 
          pattern: 'prefectureServices.map',
          description: '动态渲染地州服务列表'
        },
        { 
          name: '服务详情显示', 
          pattern: 'service.service_name',
          description: '显示服务名称和详情'
        },
        { 
          name: '价格和时间显示', 
          pattern: 'service.price_range.*service.working_hours',
          description: '显示服务价格和工作时间'
        }
      ]
      
      console.log('\n   动态服务显示检查:')
      serviceDisplayChecks.forEach(check => {
        if (pageContent.includes(check.pattern)) {
          console.log(`   ✅ ${check.name} - 已实现`)
          console.log(`      ${check.description}`)
        } else {
          console.log(`   ⚠️  ${check.name} - 可能缺失`)
          console.log(`      ${check.description}`)
        }
      })
      
    } else {
      console.log('❌ 前台页面加载失败')
    }
    
  } catch (error) {
    console.error('❌ 前台动态服务显示测试失败:', error.message)
  }
}

// 主测试函数
async function runComprehensiveOptimizationTests() {
  console.log('🎨 开始综合优化功能测试...')
  console.log('测试服务器: http://localhost:3001')
  console.log('测试时间:', new Date().toLocaleString('zh-CN'))
  
  // 等待服务器准备就绪
  console.log('\n⏳ 等待服务器准备就绪...')
  await new Promise(resolve => setTimeout(resolve, 3000))
  
  const prefectureData = await testGreenColorScheme()
  await testPrefectureNames()
  const serviceData = await testServiceManagementAPI()
  await testDynamicServiceDisplay()
  
  console.log('\n🎉 综合优化功能测试完成！')
  console.log('\n📋 优化总结:')
  console.log('✅ 绿色系颜色方案 - 统一使用绿色深浅区分服务状态')
  console.log('✅ 地州名称显示 - 在地图上显示地州简称')
  console.log('✅ 服务管理数据库 - 创建服务类型和地州服务关联表')
  console.log('✅ 服务管理API - 实现完整的服务CRUD接口')
  console.log('✅ 动态服务显示 - 前台根据数据库动态显示可用服务')
  
  console.log('\n💡 功能说明:')
  console.log('1. 颜色方案: 浅绿(未开通) → 中绿(部分开通) → 深绿(已开通)')
  console.log('2. 地州名称: 地图上显示简称，如"昆明"、"版纳"等')
  console.log('3. 服务管理: 后台可管理8种服务类型和各地州的服务配置')
  console.log('4. 动态显示: 点击地州后显示该地区的实际可用服务')
  
  if (prefectureData.length > 0) {
    console.log('\n📊 当前地州状态分布:')
    const statusCount = { '已开通': 0, '部分开通': 0, '未开通': 0 }
    prefectureData.forEach(p => statusCount[p.service_status]++)
    console.log(`   - 已开通 (深绿): ${statusCount['已开通']} 个地州`)
    console.log(`   - 部分开通 (中绿): ${statusCount['部分开通']} 个地州`)
    console.log(`   - 未开通 (浅绿): ${statusCount['未开通']} 个地州`)
  }
  
  if (serviceData.length > 0) {
    console.log('\n🛠️ 服务管理统计:')
    const servicesByType = {}
    serviceData.forEach(service => {
      if (!servicesByType[service.service_name]) {
        servicesByType[service.service_name] = 0
      }
      servicesByType[service.service_name]++
    })
    
    console.log('   各服务类型覆盖情况:')
    Object.keys(servicesByType).forEach(serviceName => {
      console.log(`   - ${serviceName}: ${servicesByType[serviceName]} 个地州`)
    })
  }
}

// 运行测试
runComprehensiveOptimizationTests().catch(console.error)
