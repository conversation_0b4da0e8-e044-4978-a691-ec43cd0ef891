import { ApiResponse, SiteSettingsFormData, CooperationFormData, CooperationApplication, PaginatedResponse, FAQ, FAQFormData, FAQCategory, PrefectureStatus, PrefectureStatusFormData, ServiceStatus } from './types/database'

// API基础URL
const API_BASE = '/api'

// 通用API调用函数
async function apiCall<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(`${API_BASE}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    })

    const data: ApiResponse<T> = await response.json()
    
    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`)
    }
    
    return data
  } catch (error) {
    console.error('API调用失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }
  }
}

// 网站设置相关API
export const settingsApi = {
  // 获取所有设置
  getAll: async (): Promise<ApiResponse<Record<string, string>>> => {
    return apiCall('/settings')
  },

  // 更新所有设置
  updateAll: async (settings: SiteSettingsFormData): Promise<ApiResponse> => {
    return apiCall('/settings', {
      method: 'POST',
      body: JSON.stringify(settings)
    })
  },

  // 更新单个设置
  updateSingle: async (key: string, value: string): Promise<ApiResponse> => {
    return apiCall('/settings', {
      method: 'PUT',
      body: JSON.stringify({ setting_key: key, setting_value: value })
    })
  }
}

// 合作申请相关API
export const cooperationApi = {
  // 获取合作申请列表
  getList: async (params?: {
    page?: number
    limit?: number
    status?: string
  }): Promise<ApiResponse<PaginatedResponse<CooperationApplication>>> => {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.set('page', params.page.toString())
    if (params?.limit) searchParams.set('limit', params.limit.toString())
    if (params?.status) searchParams.set('status', params.status)
    
    const query = searchParams.toString()
    return apiCall(`/cooperation${query ? `?${query}` : ''}`)
  },

  // 获取单个合作申请
  getById: async (id: number): Promise<ApiResponse<CooperationApplication>> => {
    return apiCall(`/cooperation/${id}`)
  },

  // 创建合作申请
  create: async (data: CooperationFormData): Promise<ApiResponse<{ id: number }>> => {
    return apiCall('/cooperation', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  },

  // 更新合作申请状态
  updateStatus: async (
    id: number, 
    status: string, 
    adminNotes?: string
  ): Promise<ApiResponse> => {
    return apiCall(`/cooperation/${id}`, {
      method: 'PUT',
      body: JSON.stringify({ status, admin_notes: adminNotes })
    })
  },

  // 删除合作申请
  delete: async (id: number): Promise<ApiResponse> => {
    return apiCall(`/cooperation/${id}`, {
      method: 'DELETE'
    })
  }
}

// 图片上传相关API
export const uploadApi = {
  // 上传图片
  uploadImage: async (
    file: File,
    usageType?: string,
    settingKey?: string
  ): Promise<ApiResponse<{ id: number; filename: string; url: string; size: number }>> => {
    const formData = new FormData()
    formData.append('file', file)
    if (usageType) formData.append('usageType', usageType)
    if (settingKey) formData.append('settingKey', settingKey)

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`)
      }

      return data
    } catch (error) {
      console.error('图片上传失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败'
      }
    }
  },

  // 获取图片列表
  getImages: async (usageType?: string): Promise<ApiResponse<any[]>> => {
    const params = new URLSearchParams()
    if (usageType) params.set('usageType', usageType)

    return apiCall(`/upload${params.toString() ? `?${params.toString()}` : ''}`)
  }
}

// FAQ相关API
export const faqApi = {
  // 获取FAQ列表
  getList: async (params?: {
    page?: number
    limit?: number
    category?: string
    active_only?: boolean
  }): Promise<ApiResponse<PaginatedResponse<FAQ>>> => {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.set('page', params.page.toString())
    if (params?.limit) searchParams.set('limit', params.limit.toString())
    if (params?.category) searchParams.set('category', params.category)
    if (params?.active_only) searchParams.set('active_only', 'true')

    const query = searchParams.toString()
    return apiCall(`/faqs${query ? `?${query}` : ''}`)
  },

  // 获取单个FAQ
  getById: async (id: number): Promise<ApiResponse<FAQ>> => {
    return apiCall(`/faqs/${id}`)
  },

  // 创建FAQ
  create: async (data: FAQFormData): Promise<ApiResponse<{ id: number }>> => {
    return apiCall('/faqs', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  },

  // 更新FAQ
  update: async (id: number, data: FAQFormData): Promise<ApiResponse> => {
    return apiCall(`/faqs/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  },

  // 删除FAQ
  delete: async (id: number): Promise<ApiResponse> => {
    return apiCall(`/faqs/${id}`, {
      method: 'DELETE'
    })
  },

  // 获取FAQ分类
  getCategories: async (activeOnly = true): Promise<ApiResponse<FAQCategory[]>> => {
    const params = activeOnly ? '?active_only=true' : ''
    return apiCall(`/faq-categories${params}`)
  }
}

// 地州状态相关API
export const prefectureStatusApi = {
  // 获取地州状态列表
  getList: async (activeOnly = true): Promise<ApiResponse<PrefectureStatus[]>> => {
    const params = activeOnly ? '?active_only=true' : ''
    return apiCall(`/prefecture-status${params}`)
  },

  // 获取单个地州状态
  getById: async (id: number): Promise<ApiResponse<PrefectureStatus>> => {
    return apiCall(`/prefecture-status/${id}`)
  },

  // 创建地州状态
  create: async (data: PrefectureStatusFormData): Promise<ApiResponse<{ id: number }>> => {
    return apiCall('/prefecture-status', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  },

  // 更新地州状态
  update: async (id: number, data: PrefectureStatusFormData): Promise<ApiResponse> => {
    return apiCall(`/prefecture-status/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  },

  // 批量更新地州状态
  batchUpdate: async (updates: Array<{ id: number; service_status: ServiceStatus }>): Promise<ApiResponse> => {
    return apiCall('/prefecture-status', {
      method: 'PUT',
      body: JSON.stringify({ updates })
    })
  },

  // 删除地州状态
  delete: async (id: number): Promise<ApiResponse> => {
    return apiCall(`/prefecture-status/${id}`, {
      method: 'DELETE'
    })
  }
}

// 工具函数
export const utils = {
  // 格式化日期
  formatDate: (date: string | Date): string => {
    const d = new Date(date)
    return d.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  },

  // 获取状态显示文本
  getStatusText: (status: string): string => {
    const statusMap: Record<string, string> = {
      'pending': '待处理',
      'contacted': '已联系',
      'completed': '已完成',
      'rejected': '已拒绝'
    }
    return statusMap[status] || status
  },

  // 获取状态样式类
  getStatusClass: (status: string): string => {
    const classMap: Record<string, string> = {
      'pending': 'bg-yellow-100 text-yellow-800',
      'contacted': 'bg-blue-100 text-blue-800',
      'completed': 'bg-green-100 text-green-800',
      'rejected': 'bg-red-100 text-red-800'
    }
    return classMap[status] || 'bg-gray-100 text-gray-800'
  }
}
