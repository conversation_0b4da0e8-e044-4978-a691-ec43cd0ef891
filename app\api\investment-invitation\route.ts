import { NextRequest, NextResponse } from 'next/server'
import { ApiResponse } from '@/lib/types/database'

// 招商入口数据类型
export interface InvestmentInvitation {
  id: number
  prefecture_code: string
  prefecture_name: string
  is_enabled: boolean
  title: string
  description: string
  contact_person: string
  contact_phone: string
  contact_email: string
  benefits: string[]
  requirements: string[]
  created_at: string
  updated_at: string
}

// 模拟招商入口数据
const mockInvestmentData: InvestmentInvitation[] = [
  {
    id: 1,
    prefecture_code: 'kunming',
    prefecture_name: '昆明市',
    is_enabled: true,
    title: '昆明市滇护通服务合作招商',
    description: '昆明市作为云南省会，医疗资源丰富，市场需求旺盛，诚邀优质服务商加盟合作',
    contact_person: '张经理',
    contact_phone: '0871-12345678',
    contact_email: '<EMAIL>',
    benefits: [
      '省会城市，市场容量大',
      '政策支持力度强',
      '医疗资源集中',
      '交通便利，辐射全省'
    ],
    requirements: [
      '具备相关服务资质',
      '有一定的资金实力',
      '认同滇护通服务理念',
      '能够提供优质服务'
    ],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 2,
    prefecture_code: 'dali',
    prefecture_name: '大理白族自治州',
    is_enabled: true,
    title: '大理州滇护通服务拓展招商',
    description: '大理州旅游资源丰富，外来人口多，医疗服务需求多样化，欢迎有实力的合作伙伴',
    contact_person: '李经理',
    contact_phone: '0872-12345678',
    contact_email: '<EMAIL>',
    benefits: [
      '旅游城市，服务需求多元',
      '政府大力支持',
      '市场潜力巨大',
      '品牌影响力强'
    ],
    requirements: [
      '有旅游服务经验优先',
      '多语言服务能力',
      '24小时服务能力',
      '应急处理能力强'
    ],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 3,
    prefecture_code: 'lijiang',
    prefecture_name: '丽江市',
    is_enabled: false,
    title: '丽江市滇护通服务招商',
    description: '丽江市国际旅游城市，医疗服务需求特殊，寻找专业合作伙伴',
    contact_person: '王经理',
    contact_phone: '0888-12345678',
    contact_email: '<EMAIL>',
    benefits: [
      '国际旅游城市',
      '高端客户群体',
      '服务溢价空间大',
      '品牌价值高'
    ],
    requirements: [
      '国际化服务标准',
      '高端医疗资源',
      '多语言团队',
      '文化敏感度高'
    ],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
]

// GET - 获取招商信息
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const prefecture_code = searchParams.get('prefecture_code')
    const enabled_only = searchParams.get('enabled_only') === 'true'
    
    let investmentData = mockInvestmentData
    
    if (prefecture_code) {
      investmentData = investmentData.filter(item => item.prefecture_code === prefecture_code)
    }
    
    if (enabled_only) {
      investmentData = investmentData.filter(item => item.is_enabled)
    }
    
    const response: ApiResponse<InvestmentInvitation[]> = {
      success: true,
      data: investmentData
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('获取招商信息失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '获取招商信息失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// POST - 创建或更新招商信息
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { prefecture_code, is_enabled, title, description, contact_person, contact_phone, contact_email, benefits, requirements } = body
    
    // 验证必需字段
    if (!prefecture_code || !title) {
      const response: ApiResponse = {
        success: false,
        error: '地州代码和标题为必填项'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 模拟创建/更新操作
    const existingIndex = mockInvestmentData.findIndex(item => item.prefecture_code === prefecture_code)
    
    if (existingIndex >= 0) {
      // 更新现有数据
      mockInvestmentData[existingIndex] = {
        ...mockInvestmentData[existingIndex],
        is_enabled: is_enabled !== false,
        title,
        description: description || '',
        contact_person: contact_person || '',
        contact_phone: contact_phone || '',
        contact_email: contact_email || '',
        benefits: benefits || [],
        requirements: requirements || [],
        updated_at: new Date().toISOString()
      }
      
      const response: ApiResponse = {
        success: true,
        message: '招商信息更新成功'
      }
      return NextResponse.json(response)
    } else {
      // 创建新数据
      const newItem: InvestmentInvitation = {
        id: mockInvestmentData.length + 1,
        prefecture_code,
        prefecture_name: prefecture_code, // 这里应该从地州映射中获取
        is_enabled: is_enabled !== false,
        title,
        description: description || '',
        contact_person: contact_person || '',
        contact_phone: contact_phone || '',
        contact_email: contact_email || '',
        benefits: benefits || [],
        requirements: requirements || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      
      mockInvestmentData.push(newItem)
      
      const response: ApiResponse = {
        success: true,
        message: '招商信息创建成功',
        data: { id: newItem.id }
      }
      return NextResponse.json(response)
    }
  } catch (error) {
    console.error('创建/更新招商信息失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '创建/更新招商信息失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// PUT - 启用/禁用招商入口
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { prefecture_code, is_enabled } = body
    
    if (!prefecture_code) {
      const response: ApiResponse = {
        success: false,
        error: '地州代码为必填项'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const existingIndex = mockInvestmentData.findIndex(item => item.prefecture_code === prefecture_code)
    
    if (existingIndex >= 0) {
      mockInvestmentData[existingIndex].is_enabled = is_enabled
      mockInvestmentData[existingIndex].updated_at = new Date().toISOString()
      
      const response: ApiResponse = {
        success: true,
        message: `招商入口已${is_enabled ? '启用' : '禁用'}`
      }
      return NextResponse.json(response)
    } else {
      const response: ApiResponse = {
        success: false,
        error: '未找到对应的招商信息'
      }
      return NextResponse.json(response, { status: 404 })
    }
  } catch (error) {
    console.error('更新招商状态失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '更新招商状态失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
