// API功能测试脚本
const API_BASE = 'http://localhost:3000/api'

// 测试网站设置API
async function testSettingsAPI() {
  console.log('\n=== 测试网站设置API ===')
  
  try {
    // 测试获取设置
    console.log('1. 测试获取网站设置...')
    const getResponse = await fetch(`${API_BASE}/settings`)
    const getResult = await getResponse.json()
    console.log('获取设置结果:', getResult.success ? '成功' : '失败')
    if (getResult.success) {
      console.log('设置数据:', Object.keys(getResult.data).length, '项设置')
    }
    
    // 测试更新设置
    console.log('2. 测试更新网站设置...')
    const updateData = {
      site_name: '滇护通测试',
      hero_title: '测试标题',
      hero_subtitle: '测试副标题',
      hero_image: '/test-image.jpg',
      about_us: '测试关于我们',
      copyright: '测试版权信息',
      icp: '测试备案号',
      phone: '************',
      address: '测试地址',
      email: '<EMAIL>'
    }
    
    const updateResponse = await fetch(`${API_BASE}/settings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateData)
    })
    const updateResult = await updateResponse.json()
    console.log('更新设置结果:', updateResult.success ? '成功' : '失败')
    
  } catch (error) {
    console.error('设置API测试失败:', error.message)
  }
}

// 测试合作申请API
async function testCooperationAPI() {
  console.log('\n=== 测试合作申请API ===')
  
  try {
    // 测试创建合作申请
    console.log('1. 测试创建合作申请...')
    const createData = {
      name: '测试用户',
      company: '测试公司',
      phone: '13800000000',
      email: '<EMAIL>',
      region: '昆明市',
      cooperation_type: '区域代理',
      experience: '有相关经验',
      message: '这是一个测试合作申请'
    }
    
    const createResponse = await fetch(`${API_BASE}/cooperation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(createData)
    })
    const createResult = await createResponse.json()
    console.log('创建申请结果:', createResult.success ? '成功' : '失败')
    
    let applicationId = null
    if (createResult.success) {
      applicationId = createResult.data.id
      console.log('新申请ID:', applicationId)
    }
    
    // 测试获取合作申请列表
    console.log('2. 测试获取合作申请列表...')
    const listResponse = await fetch(`${API_BASE}/cooperation?limit=5`)
    const listResult = await listResponse.json()
    console.log('获取列表结果:', listResult.success ? '成功' : '失败')
    if (listResult.success) {
      console.log('申请数量:', listResult.data.data.length)
      console.log('总数:', listResult.data.total)
    }
    
    // 测试更新申请状态
    if (applicationId) {
      console.log('3. 测试更新申请状态...')
      const statusResponse = await fetch(`${API_BASE}/cooperation/${applicationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: 'contacted',
          admin_notes: '测试备注'
        })
      })
      const statusResult = await statusResponse.json()
      console.log('更新状态结果:', statusResult.success ? '成功' : '失败')
    }
    
  } catch (error) {
    console.error('合作申请API测试失败:', error.message)
  }
}

// 主测试函数
async function runTests() {
  console.log('开始API功能测试...')
  console.log('API基础URL:', API_BASE)
  
  // 等待服务器启动
  console.log('等待服务器启动...')
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  await testSettingsAPI()
  await testCooperationAPI()
  
  console.log('\n=== 测试完成 ===')
}

// 运行测试
runTests().catch(console.error)
