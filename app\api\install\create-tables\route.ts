import { NextRequest, NextResponse } from 'next/server'
import mysql from 'mysql2/promise'

// POST - 创建数据库表
export async function POST(request: NextRequest) {
  let connection: mysql.Connection | null = null
  
  try {
    const { host, port, user, password, database } = await request.json()
    
    // 连接数据库
    connection = await mysql.createConnection({
      host,
      port: parseInt(port),
      user,
      password: password || undefined,
      database,
      multipleStatements: true
    })

    // 数据库表创建SQL
    const createTablesSQL = `
      -- 网站设置表
      CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT DEFAULT NULL,
        description TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_setting_key (setting_key)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

      -- 图片管理表
      CREATE TABLE IF NOT EXISTS images (
        id INT AUTO_INCREMENT PRIMARY KEY,
        filename VARCHAR(255) NOT NULL,
        original_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT NOT NULL,
        mime_type VARCHAR(100) NOT NULL,
        width INT DEFAULT NULL,
        height INT DEFAULT NULL,
        usage_type VARCHAR(50) DEFAULT NULL,
        alt_text TEXT DEFAULT NULL,
        uploaded_by VARCHAR(50) DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_usage_type (usage_type),
        INDEX idx_uploaded_by (uploaded_by),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

      -- 合作申请表
      CREATE TABLE IF NOT EXISTS cooperation_applications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        company_name VARCHAR(200) NOT NULL,
        contact_person VARCHAR(100) NOT NULL,
        phone VARCHAR(20) NOT NULL,
        email VARCHAR(100) DEFAULT NULL,
        business_type VARCHAR(100) DEFAULT NULL,
        cooperation_intention TEXT DEFAULT NULL,
        company_description TEXT DEFAULT NULL,
        status ENUM('pending', 'contacted', 'completed', 'rejected') DEFAULT 'pending',
        admin_notes TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_created_at (created_at),
        INDEX idx_phone (phone)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

      -- FAQ表
      CREATE TABLE IF NOT EXISTS faqs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        question VARCHAR(500) NOT NULL,
        answer TEXT NOT NULL,
        category VARCHAR(100) DEFAULT 'general',
        sort_order INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_category (category),
        INDEX idx_active (is_active),
        INDEX idx_sort (sort_order)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

      -- FAQ分类表
      CREATE TABLE IF NOT EXISTS faq_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT DEFAULT NULL,
        sort_order INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_active (is_active),
        INDEX idx_sort (sort_order)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

      -- 地州服务状态表
      CREATE TABLE IF NOT EXISTS prefecture_status (
        id INT AUTO_INCREMENT PRIMARY KEY,
        prefecture_name VARCHAR(100) NOT NULL,
        service_status ENUM('available', 'coming_soon', 'unavailable') DEFAULT 'coming_soon',
        description TEXT DEFAULT NULL,
        contact_phone VARCHAR(20) DEFAULT NULL,
        contact_email VARCHAR(100) DEFAULT NULL,
        sort_order INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (service_status),
        INDEX idx_active (is_active),
        INDEX idx_sort (sort_order)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

      -- 管理员表
      CREATE TABLE IF NOT EXISTS admins (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100) DEFAULT NULL,
        full_name VARCHAR(100) DEFAULT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_active (is_active)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

      -- 系统日志表
      CREATE TABLE IF NOT EXISTS system_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        level ENUM('info', 'warning', 'error', 'debug') DEFAULT 'info',
        message TEXT NOT NULL,
        context JSON DEFAULT NULL,
        user_id INT DEFAULT NULL,
        ip_address VARCHAR(45) DEFAULT NULL,
        user_agent TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_level (level),
        INDEX idx_created_at (created_at),
        INDEX idx_user_id (user_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `

    // 执行创建表的SQL
    await connection.execute(createTablesSQL)
    
    // 验证表是否创建成功
    const [tables] = await connection.execute('SHOW TABLES')
    const tableNames = (tables as any[]).map(row => Object.values(row)[0])
    
    const expectedTables = [
      'settings',
      'images', 
      'cooperation_applications',
      'faqs',
      'faq_categories',
      'prefecture_status',
      'admins',
      'system_logs'
    ]
    
    const missingTables = expectedTables.filter(table => !tableNames.includes(table))
    
    if (missingTables.length > 0) {
      throw new Error(`以下表创建失败: ${missingTables.join(', ')}`)
    }
    
    return NextResponse.json({
      success: true,
      message: '数据库表创建成功',
      tables: tableNames,
      createdTables: expectedTables.length
    })
    
  } catch (error: any) {
    console.error('创建数据库表失败:', error)
    
    let errorMessage = '创建数据库表失败'
    if (error.code === 'ER_TABLE_EXISTS_ERROR') {
      errorMessage = '表已存在'
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      errorMessage = '数据库权限不足，无法创建表'
    } else if (error.message) {
      errorMessage = error.message
    }
    
    return NextResponse.json({
      success: false,
      error: errorMessage,
      code: error.code
    }, { status: 500 })
    
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}
