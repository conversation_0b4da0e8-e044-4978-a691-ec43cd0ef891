// 添加小程序码字段的数据库迁移脚本
const mysql = require('mysql2/promise')

async function addMiniprogramQRCode() {
  console.log('🚀 开始添加小程序码字段...')
  
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '4MndSrzT8TSB7kPz',
    database: process.env.DB_NAME || 'dianfuto',
    port: parseInt(process.env.DB_PORT || '3306'),
  })

  try {
    console.log('✅ 数据库连接成功')

    // 检查字段是否已存在
    console.log('📝 检查miniprogram_qrcode字段是否存在...')
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'dianfuto' 
      AND TABLE_NAME = 'settings' 
      AND COLUMN_NAME = 'miniprogram_qrcode'
    `)

    if (columns.length === 0) {
      // 添加字段
      console.log('📝 添加miniprogram_qrcode字段到settings表...')
      await connection.execute(`
        ALTER TABLE settings 
        ADD COLUMN miniprogram_qrcode VARCHAR(500) DEFAULT NULL COMMENT '小程序码图片路径'
      `)
      console.log('✅ miniprogram_qrcode字段添加成功')
    } else {
      console.log('ℹ️  miniprogram_qrcode字段已存在，跳过添加')
    }

    // 检查设置项是否存在
    console.log('📝 检查小程序码设置项...')
    const [settings] = await connection.execute(
      'SELECT * FROM settings WHERE setting_key = ?',
      ['miniprogram_qrcode']
    )

    if (settings.length === 0) {
      // 插入设置项
      console.log('📝 插入小程序码设置项...')
      await connection.execute(`
        INSERT INTO settings (setting_key, setting_value, description) 
        VALUES ('miniprogram_qrcode', '', '小程序码图片路径')
      `)
      console.log('✅ 小程序码设置项插入成功')
    } else {
      console.log('ℹ️  小程序码设置项已存在，跳过插入')
    }

    console.log('🎉 小程序码字段添加完成！')

  } catch (error) {
    console.error('❌ 数据库迁移失败:', error)
    process.exit(1)
  } finally {
    await connection.end()
  }
}

// 运行迁移
addMiniprogramQRCode().catch(console.error)
