import { NextRequest, NextResponse } from 'next/server'
import { writeFileSync } from 'fs'
import path from 'path'

// POST - 完成安装
export async function POST(request: NextRequest) {
  try {
    // 创建安装完成标记文件
    const installLockPath = path.join(process.cwd(), '.install.lock')
    const installInfo = {
      installed: true,
      installTime: new Date().toISOString(),
      version: '1.0.0',
      installer: 'web-installer',
      nodeVersion: process.version,
      platform: process.platform
    }
    
    writeFileSync(installLockPath, JSON.stringify(installInfo, null, 2), 'utf8')
    
    // 创建安装成功页面的重定向配置
    const nextConfigPath = path.join(process.cwd(), 'next.config.js')
    
    // 记录安装完成日志
    const logPath = path.join(process.cwd(), 'logs/install.log')
    const logEntry = `[${new Date().toISOString()}] 安装完成 - 滇护通医疗陪护服务平台\n`
    
    try {
      const fs = require('fs')
      if (!fs.existsSync(path.dirname(logPath))) {
        fs.mkdirSync(path.dirname(logPath), { recursive: true })
      }
      fs.appendFileSync(logPath, logEntry, 'utf8')
    } catch (logError) {
      console.warn('写入安装日志失败:', logError)
    }

    return NextResponse.json({
      success: true,
      message: '安装完成',
      installInfo,
      nextSteps: [
        '访问管理后台: /admin',
        '使用默认账户登录',
        '配置网站信息',
        '上传小程序码',
        '开始使用系统'
      ]
    })
    
  } catch (error: any) {
    console.error('完成安装失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error.message || '完成安装失败'
    }, { status: 500 })
  }
}
