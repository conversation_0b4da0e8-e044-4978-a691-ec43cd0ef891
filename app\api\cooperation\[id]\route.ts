import { NextRequest, NextResponse } from 'next/server'
import { executeQuery, executeUpdate } from '@/lib/database'
import { CooperationApplication, ApiResponse } from '@/lib/types/database'

// GET - 获取单个合作申请详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      const response: ApiResponse = {
        success: false,
        error: '无效的ID'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const application = await executeQuery<CooperationApplication>(
      'SELECT * FROM cooperation_applications WHERE id = ?',
      [id]
    )
    
    if (application.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: '合作申请不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse<CooperationApplication> = {
      success: true,
      data: application[0]
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('获取合作申请详情失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '获取合作申请详情失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// PUT - 更新合作申请状态
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    const { status, admin_notes } = await request.json()
    
    if (isNaN(id)) {
      const response: ApiResponse = {
        success: false,
        error: '无效的ID'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    if (!status) {
      const response: ApiResponse = {
        success: false,
        error: '缺少状态参数'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 验证状态值
    const validStatuses = ['pending', 'contacted', 'completed', 'rejected']
    if (!validStatuses.includes(status)) {
      const response: ApiResponse = {
        success: false,
        error: '无效的状态值'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 更新状态
    const affectedRows = await executeUpdate(
      'UPDATE cooperation_applications SET status = ?, admin_notes = ?, updated_at = NOW() WHERE id = ?',
      [status, admin_notes || null, id]
    )
    
    if (affectedRows === 0) {
      const response: ApiResponse = {
        success: false,
        error: '合作申请不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse = {
      success: true,
      message: '状态更新成功'
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('更新合作申请状态失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '更新状态失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// DELETE - 删除合作申请
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      const response: ApiResponse = {
        success: false,
        error: '无效的ID'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const affectedRows = await executeUpdate(
      'DELETE FROM cooperation_applications WHERE id = ?',
      [id]
    )
    
    if (affectedRows === 0) {
      const response: ApiResponse = {
        success: false,
        error: '合作申请不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse = {
      success: true,
      message: '删除成功'
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('删除合作申请失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '删除失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
