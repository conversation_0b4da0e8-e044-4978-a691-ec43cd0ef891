@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 用户评价无缝循环滚动动画 */
@keyframes infinite-scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-33.333%);
  }
}

.animate-infinite-scroll {
  animation: infinite-scroll 45s linear infinite;
  width: max-content;
}

.animate-infinite-scroll:hover {
  animation-play-state: paused;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .animate-infinite-scroll {
    animation-duration: 35s; /* 手机端稍快一些 */
  }
}

/* 用户评价卡片悬停优化 */
.testimonial-card {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 10;
}

.testimonial-card:hover {
  transform: scale(1.1) translateY(-8px);
  z-index: 60 !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 渐变遮罩层级控制 */
.gradient-mask {
  z-index: 30;
}

/* 当有卡片悬停时，降低遮罩层级 */
.testimonial-scroll-container:hover .gradient-mask {
  z-index: 20;
}

/* 悬停时的额外视觉效果 */
.testimonial-card:hover .testimonial-avatar {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
}

.testimonial-card:hover .testimonial-content {
  color: #047857;
}

/* 服务覆盖区域响应式优化 */
.service-coverage-container {
  min-height: 400px;
}

@media (min-width: 768px) {
  .service-coverage-container {
    min-height: 500px;
  }
}

@media (min-width: 1024px) {
  .service-coverage-container {
    min-height: 700px;
  }
}

/* SVG地图响应式优化 */
.map-svg {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
}

/* 确保地图在小屏幕上正确显示 */
@media (max-width: 768px) {
  .map-container {
    min-height: 300px;
    max-height: 400px;
  }

  .map-svg {
    object-fit: contain;
  }
}
