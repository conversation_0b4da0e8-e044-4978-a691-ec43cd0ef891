import { NextRequest, NextResponse } from 'next/server'
import { executeQuery, executeInsert, executeUpdate } from '@/lib/database'
import { ApiResponse } from '@/lib/types/database'

// 地州服务数据类型
export interface ServiceType {
  id: number
  service_name: string
  service_code: string
  description: string
  icon: string
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface PrefectureService {
  id: number
  prefecture_id: number
  service_type_id: number
  is_available: boolean
  service_description: string
  contact_info: string
  price_range: string
  working_hours: string
  created_at: string
  updated_at: string
}

export interface PrefectureServiceView {
  prefecture_name: string
  prefecture_code: string
  service_status: string
  service_name: string
  service_code: string
  service_description: string
  icon: string
  is_available: boolean
  specific_description: string
  contact_info: string
  price_range: string
  working_hours: string
  service_updated_at: string
}

// 模拟地州服务数据
const mockPrefectureServices: PrefectureServiceView[] = [
  // 昆明市 - 已开通全部服务
  {
    prefecture_name: '昆明市',
    prefecture_code: 'kunming',
    service_status: '已开通',
    service_name: '陪诊服务',
    service_code: 'accompany_medical',
    service_description: '专业陪护人员陪同就医，提供全程陪诊服务',
    icon: '🏥',
    is_available: true,
    specific_description: '昆明市陪诊服务已全面开通，24小时提供专业陪诊',
    contact_info: '400-123-4567',
    price_range: '200-500元/次',
    working_hours: '24小时服务',
    service_updated_at: new Date().toISOString()
  },
  {
    prefecture_name: '昆明市',
    prefecture_code: 'kunming',
    service_status: '已开通',
    service_name: '医疗跑腿',
    service_code: 'medical_errand',
    service_description: '代办医疗相关事务，如取药、送检查报告等',
    icon: '🏃‍♂️',
    is_available: true,
    specific_description: '昆明市医疗跑腿服务覆盖全市各大医院',
    contact_info: '400-123-4567',
    price_range: '50-200元/次',
    working_hours: '24小时服务',
    service_updated_at: new Date().toISOString()
  },
  {
    prefecture_name: '昆明市',
    prefecture_code: 'kunming',
    service_status: '已开通',
    service_name: '上门护理',
    service_code: 'home_nursing',
    service_description: '专业护理人员提供上门护理服务',
    icon: '👩‍⚕️',
    is_available: true,
    specific_description: '昆明市上门护理服务，专业护士团队',
    contact_info: '400-123-4567',
    price_range: '150-300元/次',
    working_hours: '24小时服务',
    service_updated_at: new Date().toISOString()
  },
  // 大理州 - 部分开通
  {
    prefecture_name: '大理白族自治州',
    prefecture_code: 'dali',
    service_status: '部分开通',
    service_name: '陪诊服务',
    service_code: 'accompany_medical',
    service_description: '专业陪护人员陪同就医，提供全程陪诊服务',
    icon: '🏥',
    is_available: true,
    specific_description: '大理州陪诊服务试运行中，覆盖主要医院',
    contact_info: '************',
    price_range: '200-400元/次',
    working_hours: '工作日 9:00-18:00',
    service_updated_at: new Date().toISOString()
  },
  {
    prefecture_name: '大理白族自治州',
    prefecture_code: 'dali',
    service_status: '部分开通',
    service_name: '健康咨询',
    service_code: 'health_consultation',
    service_description: '提供专业的健康咨询和建议',
    icon: '💬',
    is_available: true,
    specific_description: '大理州健康咨询服务，在线专家团队',
    contact_info: '************',
    price_range: '免费咨询',
    working_hours: '工作日 9:00-18:00',
    service_updated_at: new Date().toISOString()
  },
  // 丽江市 - 已开通
  {
    prefecture_name: '丽江市',
    prefecture_code: 'lijiang',
    service_status: '已开通',
    service_name: '陪诊服务',
    service_code: 'accompany_medical',
    service_description: '专业陪护人员陪同就医，提供全程陪诊服务',
    icon: '🏥',
    is_available: true,
    specific_description: '丽江市陪诊服务全面开通，服务游客和本地居民',
    contact_info: '************',
    price_range: '200-450元/次',
    working_hours: '24小时服务',
    service_updated_at: new Date().toISOString()
  },
  {
    prefecture_name: '丽江市',
    prefecture_code: 'lijiang',
    service_status: '已开通',
    service_name: '医疗跑腿',
    service_code: 'medical_errand',
    service_description: '代办医疗相关事务，如取药、送检查报告等',
    icon: '🏃‍♂️',
    is_available: true,
    specific_description: '丽江市医疗跑腿服务，特别服务旅游人群',
    contact_info: '************',
    price_range: '60-250元/次',
    working_hours: '24小时服务',
    service_updated_at: new Date().toISOString()
  }
]

// GET - 获取地州服务信息
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const prefecture_code = searchParams.get('prefecture_code')
    const service_code = searchParams.get('service_code')
    const available_only = searchParams.get('available_only') === 'true'

    let services = mockPrefectureServices

    if (prefecture_code) {
      services = services.filter(service => service.prefecture_code === prefecture_code)
    }

    if (service_code) {
      services = services.filter(service => service.service_code === service_code)
    }

    if (available_only) {
      services = services.filter(service => service.is_available)
    }

    const response: ApiResponse<PrefectureServiceView[]> = {
      success: true,
      data: services
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('获取地州服务信息失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '获取地州服务信息失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// POST - 创建或更新地州服务
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { prefecture_id, service_type_id, is_available, service_description, contact_info, price_range, working_hours } = body
    
    // 验证必需字段
    if (!prefecture_id || !service_type_id) {
      const response: ApiResponse = {
        success: false,
        error: '地州ID和服务类型ID为必填项'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 检查是否已存在该地州的该服务
    const existing = await executeQuery<PrefectureService>(
      'SELECT * FROM prefecture_services WHERE prefecture_id = ? AND service_type_id = ?',
      [prefecture_id, service_type_id]
    )
    
    if (existing.length > 0) {
      // 更新现有服务
      await executeUpdate(
        `UPDATE prefecture_services 
         SET is_available = ?, service_description = ?, contact_info = ?, price_range = ?, working_hours = ?
         WHERE prefecture_id = ? AND service_type_id = ?`,
        [is_available, service_description, contact_info, price_range, working_hours, prefecture_id, service_type_id]
      )
      
      const response: ApiResponse = {
        success: true,
        message: '地州服务更新成功'
      }
      return NextResponse.json(response)
    } else {
      // 创建新服务
      const result = await executeInsert(
        `INSERT INTO prefecture_services (prefecture_id, service_type_id, is_available, service_description, contact_info, price_range, working_hours)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [prefecture_id, service_type_id, is_available, service_description, contact_info, price_range, working_hours]
      )
      
      const response: ApiResponse = {
        success: true,
        message: '地州服务创建成功',
        data: { id: result.insertId }
      }
      return NextResponse.json(response)
    }
  } catch (error) {
    console.error('创建/更新地州服务失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '创建/更新地州服务失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// PUT - 批量更新地州服务
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { prefecture_id, services } = body
    
    if (!prefecture_id || !Array.isArray(services)) {
      const response: ApiResponse = {
        success: false,
        error: '地州ID和服务列表为必填项'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // 批量更新服务
    for (const service of services) {
      const { service_type_id, is_available, service_description, contact_info, price_range, working_hours } = service
      
      // 检查是否已存在
      const existing = await executeQuery<PrefectureService>(
        'SELECT * FROM prefecture_services WHERE prefecture_id = ? AND service_type_id = ?',
        [prefecture_id, service_type_id]
      )
      
      if (existing.length > 0) {
        // 更新现有服务
        await executeUpdate(
          `UPDATE prefecture_services 
           SET is_available = ?, service_description = ?, contact_info = ?, price_range = ?, working_hours = ?
           WHERE prefecture_id = ? AND service_type_id = ?`,
          [is_available, service_description, contact_info, price_range, working_hours, prefecture_id, service_type_id]
        )
      } else {
        // 创建新服务
        await executeInsert(
          `INSERT INTO prefecture_services (prefecture_id, service_type_id, is_available, service_description, contact_info, price_range, working_hours)
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [prefecture_id, service_type_id, is_available, service_description, contact_info, price_range, working_hours]
        )
      }
    }
    
    const response: ApiResponse = {
      success: true,
      message: `成功更新 ${services.length} 个服务`
    }
    return NextResponse.json(response)
  } catch (error) {
    console.error('批量更新地州服务失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '批量更新地州服务失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
