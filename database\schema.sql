-- 滇护通数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS dianfuto CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE dianfuto;

-- 1. 网站设置表
CREATE TABLE IF NOT EXISTS site_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE COMMENT '设置键名',
    setting_value TEXT COMMENT '设置值',
    setting_type ENUM('text', 'textarea', 'image', 'json') DEFAULT 'text' COMMENT '设置类型',
    description VARCHAR(255) COMMENT '设置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站设置表';

-- 2. 合作申请表
CREATE TABLE IF NOT EXISTS cooperation_applications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '申请人姓名',
    company VARCHAR(200) COMMENT '公司名称',
    phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱地址',
    region VARCHAR(100) NOT NULL COMMENT '合作区域',
    cooperation_type VARCHAR(50) NOT NULL COMMENT '合作类型',
    experience TEXT COMMENT '相关经验',
    message TEXT NOT NULL COMMENT '合作意向说明',
    status ENUM('pending', 'contacted', 'completed', 'rejected') DEFAULT 'pending' COMMENT '处理状态',
    admin_notes TEXT COMMENT '管理员备注',
    ip_address VARCHAR(45) COMMENT '提交IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_region (region),
    INDEX idx_cooperation_type (cooperation_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合作申请表';

-- 3. 图片管理表
CREATE TABLE IF NOT EXISTS images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL COMMENT '文件名',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size INT NOT NULL COMMENT '文件大小(字节)',
    mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
    width INT COMMENT '图片宽度',
    height INT COMMENT '图片高度',
    usage_type VARCHAR(50) COMMENT '使用类型(hero_image, logo, banner等)',
    alt_text VARCHAR(255) COMMENT '图片描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    uploaded_by VARCHAR(100) COMMENT '上传者',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_usage_type (usage_type),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片管理表';

-- 4. 管理员表 (用于后台登录)
CREATE TABLE IF NOT EXISTS admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    email VARCHAR(100) COMMENT '邮箱',
    real_name VARCHAR(100) COMMENT '真实姓名',
    role ENUM('super_admin', 'admin', 'editor') DEFAULT 'admin' COMMENT '角色',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 插入默认网站设置数据
INSERT INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('site_name', '滇护通', 'text', '网站名称'),
('hero_title', '让医疗陪护更简单、更安心', 'text', '首页主标题'),
('hero_subtitle', '足不出户，为家人预约专业的陪诊、医疗跑腿、代办、上门护工等服务', 'textarea', '首页副标题'),
('hero_image', '/placeholder.svg?height=600&width=1200', 'image', '首页头图'),
('about_us', '滇护通是一家专业的医疗陪护服务平台，致力于为用户提供便捷、可靠的医疗陪护服务。我们拥有专业的陪护团队和完善的服务体系，让您的家人享受到最贴心的医疗陪护服务。', 'textarea', '关于我们'),
('copyright', '2024 滇护通医疗陪护服务平台. 保留所有权利.', 'text', '版权信息'),
('icp', '滇ICP备2024000001号', 'text', '备案号'),
('phone', '************', 'text', '联系电话'),
('address', '云南省昆明市五华区春城路100号', 'text', '联系地址'),
('email', '<EMAIL>', 'text', '联系邮箱');

-- 插入默认管理员账户 (密码: admin123)
INSERT INTO admins (username, password, email, real_name, role) VALUES
('admin', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZQZQZQZQZQZQZ', '<EMAIL>', '系统管理员', 'super_admin');

-- 插入示例合作申请数据
INSERT INTO cooperation_applications (name, company, phone, email, region, cooperation_type, message, status, created_at) VALUES
('张先生', '云南健康服务有限公司', '138****8888', '<EMAIL>', '昆明市', '区域代理', '希望成为昆明地区独家代理商，有丰富的医疗服务行业经验...', 'pending', '2024-01-15 14:30:00'),
('李女士', '大理医疗集团', '139****9999', '<EMAIL>', '大理市', '医院合作', '我们是大理地区知名医疗集团，希望与贵平台建立合作关系...', 'contacted', '2024-01-14 10:15:00');
