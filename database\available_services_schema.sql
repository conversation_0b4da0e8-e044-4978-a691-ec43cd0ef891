-- 可用服务管理数据库表结构

-- 1. 服务类型表
CREATE TABLE IF NOT EXISTS service_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    service_name VARCHAR(100) NOT NULL COMMENT '服务名称',
    service_code VARCHAR(50) NOT NULL UNIQUE COMMENT '服务代码',
    description TEXT COMMENT '服务描述',
    icon VARCHAR(100) COMMENT '服务图标',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '服务类型表';

-- 2. 地州可用服务关联表
CREATE TABLE IF NOT EXISTS prefecture_services (
    id INT PRIMARY KEY AUTO_INCREMENT,
    prefecture_id INT NOT NULL COMMENT '地州ID',
    service_type_id INT NOT NULL COMMENT '服务类型ID',
    is_available BOOLEAN DEFAULT TRUE COMMENT '是否可用',
    service_description TEXT COMMENT '服务具体描述',
    contact_info VARCHAR(200) COMMENT '联系方式',
    price_range VARCHAR(100) COMMENT '价格范围',
    working_hours VARCHAR(100) COMMENT '服务时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (prefecture_id) REFERENCES prefecture_status(id) ON DELETE CASCADE,
    FOREIGN KEY (service_type_id) REFERENCES service_types(id) ON DELETE CASCADE,
    UNIQUE KEY unique_prefecture_service (prefecture_id, service_type_id)
) COMMENT '地州可用服务关联表';

-- 3. 插入默认服务类型数据
INSERT INTO service_types (service_name, service_code, description, icon, sort_order) VALUES
('陪诊服务', 'accompany_medical', '专业陪护人员陪同就医，提供全程陪诊服务', '🏥', 1),
('医疗跑腿', 'medical_errand', '代办医疗相关事务，如取药、送检查报告等', '🏃‍♂️', 2),
('上门护理', 'home_nursing', '专业护理人员提供上门护理服务', '👩‍⚕️', 3),
('代办服务', 'proxy_service', '代办各类医疗手续和证明文件', '📋', 4),
('健康咨询', 'health_consultation', '提供专业的健康咨询和建议', '💬', 5),
('康复指导', 'rehabilitation_guidance', '专业康复师提供康复训练指导', '🤸‍♀️', 6),
('心理疏导', 'psychological_counseling', '专业心理咨询师提供心理疏导服务', '🧠', 7),
('营养配餐', 'nutrition_meal', '根据病情提供专业营养配餐服务', '🍽️', 8);

-- 4. 为已开通地州插入默认可用服务
-- 获取已开通的地州ID并插入全部服务
INSERT INTO prefecture_services (prefecture_id, service_type_id, is_available, service_description, price_range, working_hours)
SELECT 
    ps.id as prefecture_id,
    st.id as service_type_id,
    TRUE as is_available,
    CONCAT(ps.prefecture_name, '地区', st.service_name, '已全面开通') as service_description,
    CASE 
        WHEN st.service_code = 'accompany_medical' THEN '200-500元/次'
        WHEN st.service_code = 'medical_errand' THEN '50-200元/次'
        WHEN st.service_code = 'home_nursing' THEN '150-300元/次'
        WHEN st.service_code = 'proxy_service' THEN '100-300元/次'
        WHEN st.service_code = 'health_consultation' THEN '免费咨询'
        WHEN st.service_code = 'rehabilitation_guidance' THEN '200-400元/次'
        WHEN st.service_code = 'psychological_counseling' THEN '300-600元/次'
        WHEN st.service_code = 'nutrition_meal' THEN '80-150元/餐'
        ELSE '价格面议'
    END as price_range,
    '24小时服务' as working_hours
FROM prefecture_status ps
CROSS JOIN service_types st
WHERE ps.service_status = '已开通'
ON DUPLICATE KEY UPDATE 
    service_description = VALUES(service_description),
    price_range = VALUES(price_range),
    working_hours = VALUES(working_hours);

-- 5. 为部分开通地州插入部分服务
INSERT INTO prefecture_services (prefecture_id, service_type_id, is_available, service_description, price_range, working_hours)
SELECT 
    ps.id as prefecture_id,
    st.id as service_type_id,
    TRUE as is_available,
    CONCAT(ps.prefecture_name, '地区', st.service_name, '试运行中') as service_description,
    CASE 
        WHEN st.service_code = 'accompany_medical' THEN '200-500元/次'
        WHEN st.service_code = 'medical_errand' THEN '50-200元/次'
        WHEN st.service_code = 'health_consultation' THEN '免费咨询'
        ELSE '暂未开通'
    END as price_range,
    CASE 
        WHEN st.service_code IN ('accompany_medical', 'medical_errand', 'health_consultation') THEN '工作日 9:00-18:00'
        ELSE '暂未开通'
    END as working_hours
FROM prefecture_status ps
CROSS JOIN service_types st
WHERE ps.service_status = '部分开通'
AND st.service_code IN ('accompany_medical', 'medical_errand', 'health_consultation')
ON DUPLICATE KEY UPDATE 
    service_description = VALUES(service_description),
    price_range = VALUES(price_range),
    working_hours = VALUES(working_hours);

-- 6. 创建索引优化查询性能
CREATE INDEX idx_prefecture_services_prefecture ON prefecture_services(prefecture_id);
CREATE INDEX idx_prefecture_services_service_type ON prefecture_services(service_type_id);
CREATE INDEX idx_prefecture_services_available ON prefecture_services(is_available);
CREATE INDEX idx_service_types_active ON service_types(is_active);
CREATE INDEX idx_service_types_sort ON service_types(sort_order);

-- 7. 创建视图方便查询
CREATE OR REPLACE VIEW prefecture_services_view AS
SELECT 
    ps.prefecture_name,
    ps.prefecture_code,
    ps.service_status,
    st.service_name,
    st.service_code,
    st.description as service_description,
    st.icon,
    prs.is_available,
    prs.service_description as specific_description,
    prs.contact_info,
    prs.price_range,
    prs.working_hours,
    prs.updated_at as service_updated_at
FROM prefecture_status ps
LEFT JOIN prefecture_services prs ON ps.id = prs.prefecture_id
LEFT JOIN service_types st ON prs.service_type_id = st.id
WHERE ps.is_active = TRUE AND (st.is_active = TRUE OR st.is_active IS NULL)
ORDER BY ps.sort_order, st.sort_order;
