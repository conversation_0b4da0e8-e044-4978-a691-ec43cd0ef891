import mysql from 'mysql2/promise'

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'dianfuto',
  charset: 'utf8mb4',
  timezone: '+08:00',
  connectTimeout: 60000,
  acquireTimeout: 60000,
  timeout: 60000,
}

// 创建连接池
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
})

// 数据库连接函数
export async function getConnection() {
  try {
    const connection = await pool.getConnection()
    return connection
  } catch (error) {
    console.error('数据库连接失败:', error)
    throw error
  }
}

// 执行查询的辅助函数
export async function executeQuery<T = any>(
  query: string,
  params: any[] = []
): Promise<T[]> {
  const connection = await getConnection()
  try {
    const [rows] = await connection.execute(query, params)
    return rows as T[]
  } catch (error) {
    console.error('查询执行失败:', error)
    throw error
  } finally {
    connection.release()
  }
}

// 执行单个查询并返回第一行
export async function executeQuerySingle<T = any>(
  query: string,
  params: any[] = []
): Promise<T | null> {
  const results = await executeQuery<T>(query, params)
  return results.length > 0 ? results[0] : null
}

// 执行插入并返回插入ID
export async function executeInsert(
  query: string,
  params: any[] = []
): Promise<number> {
  const connection = await getConnection()
  try {
    const [result] = await connection.execute(query, params)
    return (result as any).insertId
  } catch (error) {
    console.error('插入执行失败:', error)
    throw error
  } finally {
    connection.release()
  }
}

// 执行更新/删除并返回影响行数
export async function executeUpdate(
  query: string,
  params: any[] = []
): Promise<number> {
  const connection = await getConnection()
  try {
    const [result] = await connection.execute(query, params)
    return (result as any).affectedRows
  } catch (error) {
    console.error('更新执行失败:', error)
    throw error
  } finally {
    connection.release()
  }
}

// 测试数据库连接
export async function testConnection(): Promise<boolean> {
  try {
    const connection = await getConnection()
    await connection.ping()
    connection.release()
    console.log('数据库连接成功')
    return true
  } catch (error) {
    console.error('数据库连接测试失败:', error)
    return false
  }
}

// 关闭连接池
export async function closePool(): Promise<void> {
  await pool.end()
}

export default pool
