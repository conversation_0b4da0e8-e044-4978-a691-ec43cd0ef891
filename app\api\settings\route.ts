import { NextRequest, NextResponse } from 'next/server'
import { executeQuery, executeUpdate } from '@/lib/database'
import { SiteSetting, ApiResponse, SiteSettingsFormData } from '@/lib/types/database'

// GET - 获取所有网站设置
export async function GET() {
  try {
    const settings = await executeQuery<SiteSetting>(
      'SELECT * FROM site_settings ORDER BY setting_key'
    )
    
    // 转换为键值对格式
    const settingsMap: Record<string, string> = {}
    settings.forEach(setting => {
      settingsMap[setting.setting_key] = setting.setting_value
    })
    
    const response: ApiResponse<Record<string, string>> = {
      success: true,
      data: settingsMap
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('获取网站设置失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '获取网站设置失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// POST - 更新网站设置
export async function POST(request: NextRequest) {
  try {
    const body: SiteSettingsFormData = await request.json()
    
    // 定义设置键值对
    const settingsToUpdate = [
      { key: 'site_name', value: body.site_name },
      { key: 'hero_title', value: body.hero_title },
      { key: 'hero_subtitle', value: body.hero_subtitle },
      { key: 'hero_image', value: body.hero_image },
      { key: 'site_logo', value: body.site_logo },
      { key: 'about_us', value: body.about_us },
      { key: 'copyright', value: body.copyright },
      { key: 'icp', value: body.icp },
      { key: 'phone', value: body.phone },
      { key: 'address', value: body.address },
      { key: 'email', value: body.email || '' }
    ]
    
    // 批量更新设置
    for (const setting of settingsToUpdate) {
      await executeUpdate(
        'UPDATE site_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?',
        [setting.value, setting.key]
      )
    }
    
    const response: ApiResponse = {
      success: true,
      message: '网站设置更新成功'
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('更新网站设置失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '更新网站设置失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// PUT - 更新单个设置项
export async function PUT(request: NextRequest) {
  try {
    const { setting_key, setting_value } = await request.json()
    
    if (!setting_key || setting_value === undefined) {
      const response: ApiResponse = {
        success: false,
        error: '缺少必要参数'
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const affectedRows = await executeUpdate(
      'UPDATE site_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?',
      [setting_value, setting_key]
    )
    
    if (affectedRows === 0) {
      const response: ApiResponse = {
        success: false,
        error: '设置项不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse = {
      success: true,
      message: '设置更新成功'
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('更新设置失败:', error)
    const response: ApiResponse = {
      success: false,
      error: '更新设置失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
