"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Heart,
  Users,
  Clock,
  Shield,
  Star,
  CheckCircle,
  Phone,
  MapPin,
  CreditCard,
  UserCheck,
  Stethoscope,
  Car,
  FileText,
  Home,
  ArrowRight,
  Sparkles,
  MessageCircle,
  Quote,
  Loader2,
  ChevronDown,
} from "lucide-react"
import { useEffect, useState } from "react"
import { settingsApi, faqApi, prefectureStatusApi } from "@/lib/api-client"
import { FAQ, PrefectureStatus } from "@/lib/types/database"
import { prefecturePaths, prefectureShortNames, prefectureCenters } from "@/lib/prefecture-paths"

function AnimatedStat({ target, suffix, label }: { target: number; suffix: string; label: string }) {
  const [count, setCount] = useState(0)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 },
    )

    const element = document.getElementById(`stat-${label}`)
    if (element) {
      observer.observe(element)
    }

    return () => {
      if (element) {
        observer.unobserve(element)
      }
    }
  }, [label, isVisible])

  useEffect(() => {
    if (!isVisible) return

    // 根据目标数字大小调整动画时长，数字越大动画时间越长
    const baseDuration = 2000 // 基础时长2秒
    const duration = baseDuration + (target / 100) * 50 // 根据数字大小增加时长
    const steps = 80 // 增加步数，让动画更平滑
    const increment = target / steps
    const stepDuration = duration / steps

    let currentStep = 0
    const timer = setInterval(() => {
      currentStep++
      const newCount = Math.min(Math.floor(increment * currentStep), target)
      setCount(newCount)

      if (currentStep >= steps) {
        clearInterval(timer)
        setCount(target)
      }
    }, stepDuration)

    return () => clearInterval(timer)
  }, [isVisible, target])

  return (
    <div id={`stat-${label}`} className="group">
      <div className="text-4xl font-bold text-emerald-600 mb-2 group-hover:scale-110 transition-transform duration-300">
        {count.toLocaleString()}
        {suffix}
      </div>
      <div className="text-gray-600 font-medium">{label}</div>
    </div>
  )
}

export default function HomePage() {
  const [settings, setSettings] = useState({
    site_name: "滇护通",
    hero_title: "让医疗陪护更简单、更安心",
    hero_subtitle: "足不出户，为家人预约专业的陪诊、医疗跑腿、代办、上门护工等服务",
    hero_image: "/placeholder.svg?height=600&width=1200",
    site_logo: "/placeholder.svg?height=60&width=200",
    about_us: "滇护通是一家专业的医疗陪护服务平台，致力于为用户提供便捷、可靠的医疗陪护服务。",
    copyright: "2024 滇护通医疗陪护服务平台. 保留所有权利.",
    icp: "滇ICP备2024000001号",
    phone: "************",
    address: "云南省昆明市",
    email: "<EMAIL>"
  })
  const [loading, setLoading] = useState(true)
  const [faqs, setFaqs] = useState<FAQ[]>([])
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null)
  const [prefectureStatus, setPrefectureStatus] = useState<PrefectureStatus[]>([])
  const [selectedPrefecture, setSelectedPrefecture] = useState<PrefectureStatus | null>(null)
  const [prefectureServices, setPrefectureServices] = useState<any[]>([])
  const [investmentInfo, setInvestmentInfo] = useState<any>(null)
  const [showQRCode, setShowQRCode] = useState(false)

  // 加载网站设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const response = await settingsApi.getAll()
        if (response.success && response.data) {
          setSettings({
            site_name: response.data.site_name || "滇护通",
            hero_title: response.data.hero_title || "让医疗陪护更简单、更安心",
            hero_subtitle: response.data.hero_subtitle || "足不出户，为家人预约专业的陪诊、医疗跑腿、代办、上门护工等服务",
            hero_image: response.data.hero_image || "/placeholder.svg?height=600&width=1200",
            site_logo: response.data.site_logo || "/placeholder.svg?height=60&width=200",
            about_us: response.data.about_us || "滇护通是一家专业的医疗陪护服务平台",
            copyright: response.data.copyright || "2024 滇护通医疗陪护服务平台. 保留所有权利.",
            icp: response.data.icp || "滇ICP备2024000001号",
            phone: response.data.phone || "************",
            address: response.data.address || "云南省昆明市",
            email: response.data.email || "<EMAIL>"
          })
        }
      } catch (error) {
        console.error('加载网站设置失败:', error)
      } finally {
        setLoading(false)
      }
    }

    loadSettings()
    loadFaqs()
    loadPrefectureStatus()
  }, [])

  // 获取地州服务数据
  const fetchPrefectureServices = async (prefectureCode?: string) => {
    try {
      if (prefectureCode) {
        // 从localStorage读取地州服务配置
        const saved = localStorage.getItem(`prefecture_config_${prefectureCode}`)
        if (saved) {
          const config = JSON.parse(saved)
          const services = config.services || []

          // 转换为前台显示格式
          return services.map((serviceName: string) => ({
            service_name: serviceName,
            icon: getServiceIcon(serviceName),
            specific_description: `${prefectureCode}地区${serviceName}已开通`,
            price_range: getServicePrice(serviceName),
            working_hours: '24小时服务'
          }))
        }
      }
    } catch (error) {
      console.error('获取地州服务失败:', error)
    }
    return []
  }

  // 获取服务图标
  const getServiceIcon = (serviceName: string): string => {
    const iconMap: {[key: string]: string} = {
      '陪诊服务': '🏥',
      '医疗跑腿': '🏃‍♂️',
      '上门护理': '👩‍⚕️',
      '代办服务': '📋',
      '健康咨询': '💬',
      '康复指导': '🤸‍♀️',
      '心理疏导': '🧠',
      '营养配餐': '🍽️'
    }
    return iconMap[serviceName] || '🔧'
  }

  // 获取服务价格
  const getServicePrice = (serviceName: string): string => {
    const priceMap: {[key: string]: string} = {
      '陪诊服务': '200-500元/次',
      '医疗跑腿': '50-200元/次',
      '上门护理': '150-300元/次',
      '代办服务': '100-300元/次',
      '健康咨询': '免费咨询',
      '康复指导': '200-400元/次',
      '心理疏导': '300-600元/次',
      '营养配餐': '80-150元/餐'
    }
    return priceMap[serviceName] || '价格面议'
  }

  // 获取招商信息
  const fetchInvestmentInfo = async (prefectureCode: string) => {
    try {
      // 从localStorage读取地州配置
      const saved = localStorage.getItem(`prefecture_config_${prefectureCode}`)
      if (saved) {
        const config = JSON.parse(saved)
        if (config.cooperationEnabled) {
          // 返回招商信息
          return {
            prefecture_code: prefectureCode,
            is_enabled: true,
            title: `${prefectureCode}地区滇护通服务合作招商`,
            description: '诚邀优质服务商加盟合作，共同为当地居民提供专业的医疗护理服务'
          }
        }
      }
    } catch (error) {
      console.error('获取招商信息失败:', error)
    }
    return null
  }

  // 加载地州状态数据
  const loadPrefectureStatus = async () => {
    try {
      const response = await prefectureStatusApi.getList(true) // 只获取启用的
      if (response.success && response.data) {
        setPrefectureStatus(response.data)
      }
    } catch (error) {
      console.error('加载地州状态失败:', error)
    }
  }

  // 加载FAQ数据
  const loadFaqs = async () => {
    try {
      const response = await faqApi.getList({ active_only: true, limit: 10 })
      if (response.success && response.data) {
        setFaqs(response.data.data)
      }
    } catch (error) {
      console.error('加载FAQ失败:', error)
    }
  }

  // 切换FAQ展开状态
  const toggleFaq = (faqId: number) => {
    setExpandedFaq(expandedFaq === faqId ? null : faqId)
  }

  // 地图交互函数
  const handlePrefectureClick = async (prefectureCode: string) => {
    const prefecture = prefectureStatus.find(p => p.prefecture_code === prefectureCode)
    setSelectedPrefecture(prefecture || null)

    // 加载该地州的服务数据和招商信息
    if (prefecture) {
      const services = await fetchPrefectureServices(prefectureCode)
      setPrefectureServices(services)

      const investment = await fetchInvestmentInfo(prefectureCode)
      setInvestmentInfo(investment)
    }
  }

  const handlePrefectureHover = (prefectureCode: string) => {
    // 可以添加hover效果
  }

  // 获取地州颜色
  const getPrefectureColor = (prefectureCode: string): string => {
    const prefecture = prefectureStatus.find(p => p.prefecture_code === prefectureCode)
    return prefecture?.color_code || '#CCCCCC'
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-emerald-600 mx-auto mb-4" />
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50">
      {/* Header */}
      <header className="border-b border-emerald-100 bg-white/90 backdrop-blur-md sticky top-0 z-50 shadow-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            {settings.site_logo && settings.site_logo !== '/placeholder.svg?height=60&width=200' ? (
              <img
                src={settings.site_logo}
                alt={`${settings.site_name} Logo`}
                className="h-10 w-auto object-contain"
              />
            ) : (
              <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                <Heart className="w-6 h-6 text-white" />
              </div>
            )}
            <span className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
              {settings.site_name}
            </span>
          </div>
          <nav className="hidden md:flex items-center gap-8">
            <a
              href="#services"
              className="text-gray-600 hover:text-emerald-600 transition-all duration-300 font-medium relative group"
            >
              服务介绍
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-emerald-500 transition-all duration-300 group-hover:w-full"></span>
            </a>
            <a
              href="#how-it-works"
              className="text-gray-600 hover:text-emerald-600 transition-all duration-300 font-medium relative group"
            >
              使用流程
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-emerald-500 transition-all duration-300 group-hover:w-full"></span>
            </a>
            <a
              href="#contact"
              className="text-gray-600 hover:text-emerald-600 transition-all duration-300 font-medium relative group"
            >
              联系我们
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-emerald-500 transition-all duration-300 group-hover:w-full"></span>
            </a>
          </nav>
          <div className="flex items-center gap-3">
            <Button
              onClick={() => setShowQRCode(true)}
              className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              打开小程序预约
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-24 px-4 relative overflow-hidden">
        <div
          className="absolute inset-0 bg-gradient-to-br from-emerald-100/50 via-transparent to-teal-100/50"
          style={{
            backgroundImage: settings.hero_image && settings.hero_image !== '/placeholder.svg?height=600&width=1200'
              ? `url(${settings.hero_image})`
              : undefined,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          }}
        >
          {/* 保持渐变叠加效果 */}
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-100/30 via-transparent to-teal-100/30"></div>
        </div>
        <div className="container mx-auto text-center max-w-5xl relative z-10">
          <Badge className="mb-6 bg-gradient-to-r from-emerald-100 to-teal-100 text-emerald-700 hover:from-emerald-200 hover:to-teal-200 transition-all duration-300 px-4 py-2 text-sm font-medium border-0">
            <Sparkles className="w-4 h-4 mr-2" />
            专业医疗陪护小程序
          </Badge>
          <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-8 leading-tight">
            {settings.hero_title}
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 mb-10 leading-relaxed max-w-3xl mx-auto">
            {settings.hero_subtitle}
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Button
                      className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white text-lg px-10 py-4 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 group"
            >
              <Phone className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-300" />
              打开小程序预约
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
            </Button>
            <Button
              size="lg"
              variant="outline"
              onClick={() => setShowQRCode(true)}
              className="text-lg px-10 py-4 border-2 border-emerald-200 text-emerald-700 hover:bg-emerald-50 hover:border-emerald-300 transition-all duration-300 transform hover:scale-105 bg-transparent"
            >
              查看小程序码
            </Button>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4 bg-white">
        <div className="container mx-auto max-w-6xl">
          <div className="grid md:grid-cols-4 gap-8 text-center">
            <AnimatedStat target={7000} suffix="+" label="服务用户" />
            <AnimatedStat target={500} suffix="+" label="专业陪护" />
            <AnimatedStat target={50} suffix="+" label="合作医院" />
            <AnimatedStat target={98} suffix="%" label="满意度" />
          </div>
        </div>
      </section>

      {/* Problems Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-gray-50 to-emerald-50/30">
        <div className="container mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">您是否遇到过以下问题？</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">我们理解您的困扰，滇护通就是为您解决这些问题的</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: Users,
                title: "独自就医",
                description: "年迈的父母独自去医院，排队挂号、缴费、取药，让您放心不下？",
                color: "from-orange-400 to-red-500",
              },
              {
                icon: Clock,
                title: "工作繁忙",
                description: "工作繁忙，无法抽出时间陪同家人就医？",
                color: "from-red-400 to-pink-500",
              },
              {
                icon: MapPin,
                title: "异地居住",
                description: "异地居住，无法及时照顾生病的亲人？",
                color: "from-purple-400 to-indigo-500",
              },
              {
                icon: Home,
                title: "专业护理",
                description: "出院后需要专业的上门护理，却不知如何寻找？",
                color: "from-emerald-400 to-teal-500",
              },
            ].map((item, index) => (
              <Card
                key={index}
                className="text-center hover:shadow-2xl transition-all duration-500 transform hover:scale-105 border-0 bg-white/80 backdrop-blur-sm group"
              >
                <CardHeader className="pb-4">
                  <div
                    className={`w-16 h-16 bg-gradient-to-br ${item.color} rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:rotate-6`}
                  >
                    <item.icon className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-emerald-700 transition-colors duration-300">
                    {item.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 leading-relaxed">{item.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 px-4 bg-white">
        <div className="container mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">滇护通能为您做什么？</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">专业、便捷、可靠的医疗陪护服务</p>
          </div>
          <div className="grid lg:grid-cols-2 gap-10">
            {[
              {
                icon: Stethoscope,
                title: "陪诊服务",
                description: "专业的陪诊人员全程陪同，协助挂号、就诊、取药、检查等，让您的家人不再孤单。",
                badges: ["全程陪同", "专业协助", "贴心服务"],
                color: "from-emerald-500 to-teal-600",
              },
              {
                icon: Car,
                title: "医疗跑腿",
                description: "帮您代取药品、报告单，免去排队等待的烦恼。",
                badges: ["代取药品", "取报告单", "省时省力"],
                color: "from-green-500 to-emerald-600",
              },
              {
                icon: FileText,
                title: "代办服务",
                description: "代办住院手续、报销事宜，省时省力。",
                badges: ["住院手续", "报销事宜", "专业代办"],
                color: "from-teal-500 to-cyan-600",
              },
              {
                icon: Home,
                title: "上门护理",
                description: "提供专业的居家护理服务，让您在家也能享受专业的医疗照护。",
                badges: ["居家护理", "专业照护", "上门服务"],
                color: "from-cyan-500 to-blue-600",
              },
            ].map((item, index) => (
              <Card
                key={index}
                className="hover:shadow-2xl transition-all duration-500 transform hover:scale-105 border-0 bg-gradient-to-br from-white to-emerald-50/30 group overflow-hidden relative"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-teal-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <CardHeader className="relative z-10">
                  <div className="flex items-center gap-4 mb-4">
                    <div
                      className={`w-12 h-12 bg-gradient-to-br ${item.color} rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:rotate-12`}
                    >
                      <item.icon className="w-6 h-6 text-white" />
                    </div>
                    <CardTitle className="text-2xl font-bold text-gray-900 group-hover:text-emerald-700 transition-colors duration-300">
                      {item.title}
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="relative z-10">
                  <p className="text-gray-600 mb-6 leading-relaxed text-lg">{item.description}</p>
                  <div className="flex flex-wrap gap-3">
                    {item.badges.map((badge, badgeIndex) => (
                      <Badge
                        key={badgeIndex}
                        className="bg-gradient-to-r from-emerald-100 to-teal-100 text-emerald-700 hover:from-emerald-200 hover:to-teal-200 transition-all duration-300 px-3 py-1 border-0"
                      >
                        {badge}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Service Coverage Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-emerald-50 to-teal-50">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">服务覆盖区域</h2>
            <p className="text-xl text-gray-600">目前已覆盖云南省主要城市，持续扩展中</p>
          </div>

          <div className="service-coverage-container bg-white rounded-3xl p-3 md:p-6 shadow-xl min-h-[400px] md:min-h-[500px] lg:min-h-[700px]">
            <div className="grid lg:grid-cols-3 gap-3 md:gap-6 items-start h-full">
              {/* SVG Map */}
              <div className="lg:col-span-2 h-full">
                <div className="relative h-full">
                  <div className="relative rounded-2xl p-1 md:p-2 h-full">
                    {/* 云南省SVG地图 */}
                    <div className="relative h-full">
                      <div className="map-container w-full h-full min-h-[300px] md:min-h-[400px] lg:min-h-[650px]">
                        <div className="relative w-full h-full">
                          {/* 背景SVG地图 */}
                          <svg
                            viewBox="0 0 1700 1774"
                            className="map-svg w-full h-full opacity-30"
                            style={{
                              filter: "hue-rotate(120deg) saturate(0.8) brightness(1.2) contrast(0.9)",
                              maxHeight: "100%",
                              objectFit: "contain"
                            }}
                            preserveAspectRatio="xMidYMid meet"
                          >
                            <g clipPath="url(#clip0_52_3658)">
                              {/* 使用原始SVG的所有path，但设置为灰色背景 */}
                              <path d="M610.513 311.71H602.318L598.248 307.666H594.15V303.595H590.079L594.15 299.523V291.328H590.079V283.134H586.034V279.088H581.962V274.991H577.865V270.92H573.819V254.402H569.722V242.137H573.819V238.066H577.865V242.137H581.962V238.066H594.253L598.35 233.994V229.923H602.422V225.826H598.35V221.755H594.253V217.633H598.35V209.44H594.253V205.368H586.034V201.297H577.839V197.225H581.937V184.96H573.769V180.889H569.671V176.817H565.599V172.746L569.671 168.674V164.628H565.599V160.531H561.426V152.338H557.354V148.266H549.16V140.073H545.063V144.145H540.992V131.877H536.895V127.78H532.849L528.752 123.709H524.681V115.516L520.584 111.444H524.681V95.0826H520.584L516.512 99.1516H504.221V103.223H500.15V115.49H496.053V119.715H487.885V123.812H483.787V127.884H487.885V136.077H483.787V140.176H475.412V144.22H471.367V148.317L467.271 152.388H459.076V156.485H455.004V164.681H459.076V172.874H455.004V181.042H459.076L463.147 185.114H459.076V189.185L455.004 193.257V201.45H450.908V205.496H455.004V213.69H450.908V221.886H446.888V230.079H442.843V225.982H438.771V221.911H434.7V213.717H430.628V201.425H422.434V193.257H418.389V189.185H414.292V180.992H410.22V172.796H414.292V160.35H418.389V156.306H414.292V127.652H410.22V103.07H406.124V99.0233H410.22V94.9518H406.124V82.6618L410.22 78.5903V70.2938H414.292V8.91426H418.389V4.8176H414.292V0.746094H410.22V4.8176H389.735V8.91426H385.406V21.1791H389.503V25.2758H393.6V41.6649H385.406V45.7364H381.36V49.8331H377.315V53.9046H373.244V70.2661H377.315V74.3653H381.412V82.5586H373.218V86.605H369.12V90.6765H365.05V86.605H356.701V82.5586H352.604V49.8079L348.533 45.7113V41.6398H344.461V45.7113H336.215V49.8079H328.022V45.7113H319.854V53.9046L323.95 57.9761V62.0476H319.854V70.241L323.95 74.3376V78.4091H319.854V86.605H323.95V102.966H319.854L315.782 107.038H307.742V119.303H311.814V123.399H307.742L311.814 127.471V143.86H307.742V152.028H315.937V156.125H320.007V168.417H324.105V176.404H328.15V188.694H324.105V192.766H320.007V209.13H315.937V229.744H320.007V237.912H324.105V245.977H328.15V250.049H332.247V254.12H328.15L332.247 258.192V262.289H328.15V290.943H332.247V295.014H336.087V303.207H340.183V315.55H336.087L332.015 319.622V323.693H336.087V327.74H340.183V331.809H344.255V344.101H340.183V360.463H344.255V364.509H340.183V401.356H348.378V405.428H356.701V417.693H352.604V429.985L348.533 434.057V438.1H344.436L348.533 442.172V458.561H344.436V462.658H348.533V466.704H352.604V470.776H348.533V478.969H381.309V483.04H397.671V478.969H401.769V466.679H409.963V474.872L414.034 478.969V487.162H418.106V503.655H422.177V499.583H426.274V491.415H442.662V483.04H438.59V478.969H434.52V446.218H430.448V434.442H434.52V426.351H430.448V422.28L434.52 418.208H438.59V393.651H434.52V389.582H438.59V381.386H454.953V369.121H463.147V365.05H467.219V385.511H471.316V389.582H475.232V393.651H471.187V397.75H475.232V405.918H479.329V409.99H483.401V414.087H479.329V422.28H483.401V430.473H487.497V434.52H491.569V438.591H495.847V446.784H504.041V450.856H508.138L504.041 454.927H508.138V458.999H512.209V463.07H516.306V475.31H524.474V483.503H528.546V491.672H532.642V520.353H540.837V516.282H549.032V508.086H553.077V495.924H557.173V475.31H553.077V467.117H557.173V458.949H565.419V454.877H569.465V450.806H573.562V442.61H581.756V438.541H585.827V434.494H594.022V418.105H589.95V401.744H586.034V389.451H590.13V385.38H594.202V381.336H598.298V373.14L602.345 369.069V365H606.416V360.928H610.513V352.76H606.416V348.661H610.513V340.339H614.378V328.047H610.307V323.975H614.378V311.71H610.513Z" fill="#E5E7EB" stroke="#D1D5DB" strokeWidth="1"/>
                              {/* 其他地州的path也设置为相同的灰色背景 */}
                              <path d="M368.995 667.846H373.091V671.917H368.995V667.846ZM463.124 553.232H459.054V536.868H454.982V524.603H450.884V520.532H459.079V516.538H455.007V508.345H450.911V504.274H446.891V500.227H442.846V492.034H426.276V500.227H422.18V504.274H418.108V487.91H414.012V479.716L409.94 475.62V467.451H401.772V479.716H397.674V483.788H381.26V479.716H348.509V471.523H352.58V467.477H348.509V463.405H344.412V459.308H348.509V442.919L344.412 438.875H348.509V434.804L352.58 430.732V418.44H356.704V406.175H348.509V402.104H340.316V365.256H344.387V361.21H340.316V344.539H344.387V332.274H340.316V328.202H336.218V324.131H332.147V320.059L336.218 315.988H340.316V303.723H336.218V295.53H332.147V291.431H328.05V262.777H332.147V258.68L328.05 254.608H332.147V250.565H328.05V246.493H323.978V238.247H319.881V230.054H315.836V213.69H303.545V221.885H299.474V217.814H291.202V213.742H287.131V217.814H283.034L278.963 221.885H270.768V225.929H262.575V230.026H258.528L254.432 225.929H258.528V213.664H254.432V189.107H250.36V180.914H246.263V164.371H242.192V168.443H229.901V172.514H225.829V176.586L229.901 180.657V188.825H233.997V197.019H229.901V201.09H217.558V213.38H209.39V225.647H213.435V229.744H209.39V237.937H205.293V258.398H213.461V266.591H217.558V270.638H213.461V274.709H209.39V282.903H213.461V286.974H217.558V303.336H221.629V307.407H213.435V311.479H217.558V315.55H221.629V323.718H225.726V327.79H229.797V352.347H233.894V356.419H242.089V352.347H246.16V348.251H254.328V323.693H258.425V327.765H262.497V331.836H270.691V335.908H274.763V331.836H278.833V340.029H282.904V344.101H287.002V348.147H295.171V344.101H299.242V352.269H303.312V360.462H299.242V368.658L303.312 372.73V376.774L299.242 372.73V393.188H303.312V401.356H307.384V405.428H303.312V413.621H299.242V417.693H303.312V421.764H299.242V425.836H295.171V438.1L299.242 442.172H295.171V454.464H303.364V466.729H299.294V499.48H295.222V507.673H299.294V528.134H295.222V536.327H299.294V560.885L295.222 564.956V569.053H291.254V589.513L287.207 593.585H291.254V597.656H287.131V605.825H283.034V614.018H278.963V618.115H274.891V630.407H270.819V634.476H279.014V642.672H283.059V650.865H274.891V654.937H278.963V659.008H270.768V663.055H266.671V654.886H262.575V650.815H258.503V654.886H254.405V650.815H250.36V667.176H254.405V691.759H262.6V695.83H266.696V704.024H270.794V720.388H274.866V724.484H270.794V732.678H274.866V753.138H278.936V757.235H274.866V781.792H299.422V769.525H295.35V765.454H299.422V736.802H307.745V740.899H315.913V744.97H319.985V749.014H328.179V744.97H332.276V732.678H336.347V736.749L340.444 740.846V736.749H352.71L348.639 732.678V720.388L344.541 716.341V704.049H340.496V699.98H332.301V663.13H336.372V659.059H352.735V663.13H360.93V667.202H365.026V659.008L369.098 654.962H373.195V650.89H377.241L381.338 646.819H393.629V642.8H397.7V638.729H393.629V634.657L397.7 630.561V626.514H401.797V630.561H405.662V626.514H409.759V622.443H417.927V626.514L421.999 622.443L426.096 618.371L430.167 614.275L434.238 610.203H438.31V597.913H434.238V589.745H438.31V581.552H434.238V577.48H438.31V573.227H446.504L450.549 569.156H462.841V556.891H466.912V552.82L463.124 553.232Z" fill="#E5E7EB" stroke="#D1D5DB" strokeWidth="1"/>
                              {/* 继续添加其他path... */}
                            </g>
                            <defs>
                              <clipPath id="clip0_52_3658">
                                <rect width="1700" height="1774" fill="white"/>
                              </clipPath>
                            </defs>
                          </svg>

                          {/* 前景交互式地州区域 */}
                          <svg
                            viewBox="0 0 1700 1774"
                            className="map-svg absolute inset-0 w-full h-full"
                            style={{
                              filter: "drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))",
                              maxHeight: "100%",
                              objectFit: "contain"
                            }}
                            preserveAspectRatio="xMidYMid meet"
                          >
                            {/* 交互式地州区域 - 使用实际的SVG路径 */}
                            {prefectureStatus.map((prefecture) => {
                              const path = prefecturePaths[prefecture.prefecture_code]
                              const center = prefectureCenters[prefecture.prefecture_code]
                              const shortName = prefectureShortNames[prefecture.prefecture_code]

                              if (!path || !center) return null

                              return (
                                <g key={prefecture.id}>
                                  {/* 地州区域路径 */}
                                  <path
                                    d={path}
                                    fill={prefecture.color_code}
                                    stroke="#ffffff"
                                    strokeWidth="2"
                                    className="prefecture-path transition-all duration-300 cursor-pointer hover:stroke-width-4"
                                    style={{
                                      filter: selectedPrefecture?.id === prefecture.id
                                        ? 'drop-shadow(0 0 15px rgba(5, 150, 105, 1)) brightness(1.2)'
                                        : 'none',
                                      opacity: selectedPrefecture?.id === prefecture.id ? 1 : 0.85
                                    }}
                                    onClick={() => handlePrefectureClick(prefecture.prefecture_code)}
                                    onMouseEnter={() => handlePrefectureHover(prefecture.prefecture_code)}
                                  />

                                  {/* 地州名称标签 */}
                                  <text
                                    x={center.x}
                                    y={center.y}
                                    textAnchor="middle"
                                    dominantBaseline="middle"
                                    className="pointer-events-none select-none"
                                    style={{
                                      fill: '#1c1c1c',
                                      fontSize: '32px',
                                      fontWeight: '600',
                                      strokeWidth: '0.5px'
                                    }}
                                  >
                                    {shortName}
                                  </text>
                                </g>
                              )
                            })}


                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 服务统计和地州详情 */}
              <div className="lg:col-span-1 mt-4 lg:mt-0">
                {selectedPrefecture ? (
                  <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-2xl p-4 md:p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-xl font-bold text-gray-900">{selectedPrefecture.prefecture_name}</h3>
                      <button
                        onClick={() => setSelectedPrefecture(null)}
                        className="text-gray-500 hover:text-gray-700 transition-colors text-sm font-medium"
                      >
                        全部
                      </button>
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: selectedPrefecture.color_code }}
                        ></div>
                        <span className="font-medium" style={{ color: selectedPrefecture.color_code }}>
                          {selectedPrefecture.service_status}
                        </span>
                      </div>
                      {selectedPrefecture.description && (
                        <div className="bg-white rounded-lg p-4">
                          <p className="text-gray-700 text-sm leading-relaxed">
                            {selectedPrefecture.description}
                          </p>
                        </div>
                      )}
                      {/* 动态显示可用服务 */}
                      {prefectureServices.length > 0 && (
                        <div className="bg-emerald-100 border border-emerald-200 rounded-lg p-4">
                          <h4 className="font-medium text-emerald-800 mb-3">可用服务</h4>
                          <div className="space-y-3">
                            {prefectureServices.map((service, index) => (
                              <div key={index} className="bg-white rounded-lg p-3">
                                <div className="flex items-center gap-2 mb-2">
                                  <span className="font-medium text-emerald-800">{service.service_name}</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      {prefectureServices.length === 0 && selectedPrefecture.service_status === '未开通' && (
                        <div className="bg-gray-100 border border-gray-200 rounded-lg p-4">
                          <h4 className="font-medium text-gray-800 mb-2">服务状态</h4>
                          <p className="text-sm text-gray-600">该地区服务正在筹备中，敬请期待</p>
                        </div>
                      )}

                      {/* 合作加盟按钮 */}
                      {investmentInfo && (
                        <div className="mt-4 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-lg p-4 text-white">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium mb-1">合作机会</h4>
                              <p className="text-sm opacity-90">诚邀优质合作伙伴加盟</p>
                            </div>
                            <a
                              href={`/cooperation?prefecture=${selectedPrefecture.prefecture_code}`}
                              className="bg-white text-emerald-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                            >
                              合作加盟
                            </a>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-2xl p-4 md:p-6">
                    <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">服务覆盖统计</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm">
                      <span className="text-gray-700 font-medium">覆盖城市</span>
                      <span className="text-2xl font-bold text-emerald-600">12个</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm">
                      <span className="text-gray-700 font-medium">服务网点</span>
                      <span className="text-2xl font-bold text-emerald-600">50+</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm">
                      <span className="text-gray-700 font-medium">陪护人员</span>
                      <span className="text-2xl font-bold text-emerald-600">500+</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm">
                      <span className="text-gray-700 font-medium">合作医院</span>
                      <span className="text-2xl font-bold text-emerald-600">50+</span>
                    </div>
                  </div>

                  <div className="mt-6 p-4 bg-gradient-to-r from-emerald-600 to-teal-600 rounded-xl text-white text-center">
                    <p className="text-sm mb-2">服务覆盖率</p>
                    <p className="text-3xl font-bold">85%</p>
                    <p className="text-xs text-emerald-100">云南省主要城市</p>
                  </div>

                  <div className="mt-4 text-center">
                    <p className="text-sm text-gray-600">点击地图上的地州查看详细信息</p>
                  </div>

                  {/* 服务状态图例 */}
                  <div className="mt-6 bg-white rounded-lg p-4 border border-gray-100">
                    <h4 className="text-base font-medium text-gray-900 mb-3">服务状态</h4>
                    <div className="space-y-2">
                      <div className="flex items-center gap-3">
                        <div className="w-4 h-4 rounded-full" style={{ backgroundColor: '#059669' }}></div>
                        <span className="text-sm text-gray-700">已开通</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-4 h-4 rounded-full" style={{ backgroundColor: '#34D399' }}></div>
                        <span className="text-sm text-gray-700">部分开通</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-4 h-4 rounded-full" style={{ backgroundColor: '#A7F3D0' }}></div>
                        <span className="text-sm text-gray-700">未开通</span>
                      </div>
                    </div>
                  </div>
                </div>
                )}
              </div>
            </div>


          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-emerald-50 to-teal-50 overflow-hidden">
        <div className="container mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">用户评价</h2>
            <p className="text-xl text-gray-600">真实用户反馈，见证我们的专业服务</p>
          </div>

          {/* 滚动容器 */}
          <div className="testimonial-scroll-container relative overflow-x-hidden overflow-y-visible py-5">
            <div className="flex animate-infinite-scroll space-x-8">
              {/* 创建三组重复数据实现无缝循环 */}
              {[...Array(3)].map((_, groupIndex) =>
                [
                {
                  name: "张女士",
                  age: "45岁",
                  content:
                    "父亲住院期间，滇护通的陪护人员非常专业，帮助我们处理了很多繁琐的事务，让我能安心工作。服务态度特别好，很有耐心。",
                  rating: 5,
                  service: "陪诊服务",
                  avatar: "张",
                  location: "昆明市",
                },
                {
                  name: "李先生",
                  age: "38岁",
                  content:
                    "工作太忙无法陪母亲看病，通过滇护通找到了专业的陪护，服务很贴心，价格也合理。陪护人员很细心，让我很放心。",
                  rating: 5,
                  service: "医疗跑腿",
                  avatar: "李",
                  location: "大理市",
                },
                {
                  name: "王女士",
                  age: "52岁",
                  content:
                    "出院后需要专业护理，滇护通的上门护理服务让我们在家也能享受专业照护，非常满意。护理人员技术娴熟，服务周到。",
                  rating: 5,
                  service: "上门护理",
                  avatar: "王",
                  location: "丽江市",
                },
                {
                  name: "陈先生",
                  age: "42岁",
                  content:
                    "老父亲腿脚不便，每次去医院都很麻烦。有了滇护通，陪护人员会提前到家接送，还帮忙挂号排队，真的太方便了！",
                  rating: 5,
                  service: "陪诊服务",
                  avatar: "陈",
                  location: "曲靖市",
                },
                {
                  name: "刘女士",
                  age: "35岁",
                  content:
                    "怀孕期间行动不便，滇护通的代办服务帮我取药、拿报告，省了很多跑腿的麻烦。工作人员很负责，每次都会确认清楚。",
                  rating: 5,
                  service: "代办服务",
                  avatar: "刘",
                  location: "玉溪市",
                },
                {
                  name: "赵先生",
                  age: "48岁",
                  content:
                    "母亲手术后需要专业护理，滇护通安排的护工非常专业，不仅技术好，人也很温和，让全家都很安心。强烈推荐！",
                  rating: 5,
                  service: "上门护理",
                  avatar: "赵",
                  location: "保山市",
                },
                {
                  name: "孙女士",
                  age: "29岁",
                  content:
                    "第一次当妈妈，很多事情不懂。滇护通的月嫂服务很专业，不仅照顾宝宝，还教我很多育儿知识，真的帮了大忙。",
                  rating: 5,
                  service: "月嫂服务",
                  avatar: "孙",
                  location: "楚雄市",
                },
                {
                  name: "周先生",
                  age: "55岁",
                  content:
                    "岳父突发疾病住院，我们在外地赶不回来。滇护通的紧急陪护服务24小时待命，让我们在外地也能安心工作。",
                  rating: 5,
                  service: "紧急陪护",
                  avatar: "周",
                  location: "红河州",
                },
                ].map((testimonial, index) => (
                  <Card
                    key={`${groupIndex}-${index}`}
                    className="testimonial-card bg-white border-0 flex-shrink-0 w-80 group relative"
                  >
                    <CardContent className="p-6">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="testimonial-avatar w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg transition-all duration-300">
                          {testimonial.avatar}
                        </div>
                        <div>
                          <div className="font-semibold text-gray-900">{testimonial.name}</div>
                          <div className="text-sm text-gray-500">
                            {testimonial.age} · {testimonial.location}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-1 mb-4">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      <Quote className="w-6 h-6 text-emerald-200 mb-3" />
                      <p className="testimonial-content text-gray-600 mb-6 leading-relaxed text-sm transition-colors duration-300">{testimonial.content}</p>
                      <div className="flex items-center justify-between">
                        <Badge className="bg-emerald-100 text-emerald-700 border-0 text-xs px-2 py-1">
                          {testimonial.service}
                        </Badge>
                        <div className="text-xs text-gray-400">{Math.floor(Math.random() * 30 + 1)}天前</div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ).flat()}
            </div>

            {/* 左右渐变遮罩，让滚动效果更自然 */}
            <div className="gradient-mask absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-emerald-50 to-transparent pointer-events-none z-30"></div>
            <div className="gradient-mask absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-emerald-50 to-transparent pointer-events-none z-30"></div>
          </div>

          {/* 统计信息 */}
        </div>

        {/* CSS动画样式 */}
        <style jsx>{`
          @keyframes infinite-scroll {
            0% {
              transform: translateX(0);
            }
            100% {
              transform: translateX(-33.333%);
            }
          }

          .animate-infinite-scroll {
            animation: infinite-scroll 45s linear infinite;
            width: max-content;
          }

          .animate-infinite-scroll:hover {
            animation-play-state: paused;
          }
        `}</style>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">为什么选择滇护通？</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">我们的优势让您更安心</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
            {[
              {
                icon: Clock,
                title: "实时跟踪",
                description: "随时掌握订单状态、服务进度，了解陪护人员的实时位置",
                color: "from-emerald-500 to-teal-600",
              },
              {
                icon: Shield,
                title: "安全可靠",
                description: "严格筛选陪护人员，实名认证和专业培训，确保服务质量",
                color: "from-green-500 to-emerald-600",
              },
              {
                icon: CreditCard,
                title: "费用透明",
                description: "服务费用公开透明，所有消费记录可查询，让您消费更放心",
                color: "from-teal-500 to-cyan-600",
              },
              {
                icon: UserCheck,
                title: "专业认证",
                description: "查看陪护人员的资质、经验、用户评价，选择最适合的服务",
                color: "from-cyan-500 to-blue-600",
              },
              {
                icon: Star,
                title: "服务评价",
                description: "服务完成后可进行评价，帮助小程序提升服务质量",
                color: "from-blue-500 to-indigo-600",
              },
              {
                icon: Heart,
                title: "贴心服务",
                description: "如有不满意可投诉，我们将及时处理，保障您的权益",
                color: "from-indigo-500 to-purple-600",
              },
            ].map((item, index) => (
              <div key={index} className="text-center group">
                <div
                  className={`w-20 h-20 bg-gradient-to-br ${item.color} rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-xl group-hover:shadow-2xl transition-all duration-500 transform group-hover:scale-110 group-hover:rotate-6`}
                >
                  <item.icon className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-900 group-hover:text-emerald-700 transition-colors duration-300">
                  {item.title}
                </h3>
                <p className="text-gray-600 leading-relaxed text-lg">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="py-20 px-4 bg-gradient-to-r from-gray-50 to-emerald-50/30">
        <div className="container mx-auto max-w-5xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">如何使用滇护通？</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">简单6步，轻松预约专业服务</p>
          </div>
          <div className="space-y-12">
            {[
              {
                step: 1,
                title: "打开小程序",
                description: "微信搜索滇护通小程序或扫描小程序码，快速进入服务平台",
                icon: UserCheck,
                color: "from-emerald-500 to-teal-600",
              },
              {
                step: 2,
                title: "选择服务",
                description: "根据您的需求，选择相应的服务类型，如陪诊服务、医疗跑腿等",
                icon: Stethoscope,
                color: "from-green-500 to-emerald-600",
              },
              {
                step: 3,
                title: "填写信息",
                description: "填写就诊人信息、就诊医院、服务时间等详细信息",
                icon: FileText,
                color: "from-teal-500 to-cyan-600",
              },
              {
                step: 4,
                title: "选择陪护人员",
                description: "根据您的需求，选择合适的陪护人员",
                icon: Users,
                color: "from-cyan-500 to-blue-600",
              },
              {
                step: 5,
                title: "支付订单",
                description: "确认订单信息，选择支付方式进行支付",
                icon: CreditCard,
                color: "from-blue-500 to-indigo-600",
              },
              {
                step: 6,
                title: "享受服务",
                description: "陪护人员将在约定时间到达指定地点，为您提供专业的服务",
                icon: CheckCircle,
                color: "from-indigo-500 to-purple-600",
              },
            ].map((item, index) => (
              <div key={index} className="flex items-start gap-8 group">
                <div className="flex-shrink-0">
                  <div
                    className={`w-16 h-16 bg-gradient-to-br ${item.color} rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-xl group-hover:shadow-2xl transition-all duration-500 transform group-hover:scale-110 group-hover:rotate-6`}
                  >
                    {item.step}
                  </div>
                </div>
                <div className="flex-1 pt-2">
                  <div className="flex items-center gap-4 mb-4">
                    <item.icon className="w-8 h-8 text-emerald-600 group-hover:text-emerald-700 transition-colors duration-300" />
                    <h3 className="text-2xl font-bold text-gray-900 group-hover:text-emerald-700 transition-colors duration-300">
                      {item.title}
                    </h3>
                  </div>
                  <p className="text-gray-600 text-lg leading-relaxed">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto max-w-4xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">常见问题</h2>
            <p className="text-xl text-gray-600">为您解答使用过程中的疑问</p>
          </div>
          <div className="space-y-4">
            {faqs.map((faq) => (
              <Card
                key={faq.id}
                className="hover:shadow-lg transition-all duration-300 border border-emerald-100 bg-gradient-to-r from-white to-emerald-50/30 overflow-hidden"
              >
                <CardContent className="p-0">
                  <button
                    onClick={() => toggleFaq(faq.id)}
                    className="w-full p-6 text-left hover:bg-emerald-50/50 transition-colors duration-200 focus:outline-none"
                  >
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-3 flex-1">
                        <MessageCircle className="w-5 h-5 text-emerald-600 flex-shrink-0" />
                        <span className="pr-4">{faq.question}</span>
                      </h3>
                      <ChevronDown
                        className={`w-5 h-5 text-emerald-600 transition-transform duration-200 flex-shrink-0 ${
                          expandedFaq === faq.id ? 'rotate-180' : ''
                        }`}
                      />
                    </div>
                  </button>
                  <div
                    className={`overflow-hidden transition-all duration-300 ease-in-out ${
                      expandedFaq === faq.id ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                    }`}
                  >
                    <div className="px-6 pb-6 pt-0">
                      <div className="pl-8 border-l-2 border-emerald-200">
                        <p className="text-gray-600 leading-relaxed whitespace-pre-line">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-br from-emerald-600 via-teal-600 to-cyan-600 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-600/90 to-teal-600/90"></div>
        <div className="container mx-auto text-center max-w-5xl relative z-10">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">滇护通，您身边的医疗陪护专家</h2>
          <p className="text-xl md:text-2xl mb-10 text-emerald-100 leading-relaxed max-w-4xl mx-auto">
            我们深知您对家人的关爱，我们将竭诚为您提供优质、便捷、安心的医疗陪护服务，
            让您不再为家人的就医问题而烦恼。选择滇护通，让爱不缺席！
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Button
              size="lg"
              className="bg-white text-emerald-600 hover:bg-gray-100 hover:text-emerald-700 text-lg px-10 py-4 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 group"
            >
              <Phone className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-300" />
              立即预约服务
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
            </Button>
            <Button
              size="lg"
              variant="outline"
              onClick={() => setShowQRCode(true)}
              className="border-2 border-white text-white hover:bg-white hover:text-emerald-600 text-lg px-10 py-4 bg-transparent transition-all duration-300 transform hover:scale-105"
            >
              打开小程序
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer id="contact" className="bg-gray-900 text-white py-16 px-4">
        <div className="container mx-auto max-w-7xl">
          <div className="grid md:grid-cols-4 gap-10">
            <div>
              <div className="flex items-center gap-3 mb-6">
                {settings.site_logo && settings.site_logo !== '/placeholder.svg?height=60&width=200' ? (
                  <img
                    src={settings.site_logo}
                    alt={`${settings.site_name} Logo`}
                    className="h-10 w-auto object-contain brightness-0 invert"
                  />
                ) : (
                  <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                    <Heart className="w-6 h-6 text-white" />
                  </div>
                )}
                <span className="text-2xl font-bold bg-gradient-to-r from-emerald-400 to-teal-400 bg-clip-text text-transparent">
                  {settings.site_name}
                </span>
              </div>
              <p className="text-gray-400 text-lg leading-relaxed mb-4">{settings.hero_title}</p>
              <p className="text-gray-500 text-sm">备案号：{settings.icp}</p>
            </div>
            <div>
              <h4 className="font-bold text-xl mb-6 text-emerald-400">服务项目</h4>
              <ul className="space-y-3 text-gray-400">
                <li className="hover:text-emerald-400 transition-colors duration-300 cursor-pointer">陪诊服务</li>
                <li className="hover:text-emerald-400 transition-colors duration-300 cursor-pointer">医疗跑腿</li>
                <li className="hover:text-emerald-400 transition-colors duration-300 cursor-pointer">代办服务</li>
                <li className="hover:text-emerald-400 transition-colors duration-300 cursor-pointer">上门护理</li>
              </ul>
            </div>
            <div>
              <h4 className="font-bold text-xl mb-6 text-emerald-400">关于我们</h4>
              <ul className="space-y-3 text-gray-400">
                <li className="hover:text-emerald-400 transition-colors duration-300 cursor-pointer">公司介绍</li>
                <li className="hover:text-emerald-400 transition-colors duration-300 cursor-pointer">服务保障</li>
                <li className="hover:text-emerald-400 transition-colors duration-300 cursor-pointer">加入我们</li>
                <li className="hover:text-emerald-400 transition-colors duration-300 cursor-pointer">
                  <a href="/cooperation">合作共赢</a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-bold text-xl mb-6 text-emerald-400">联系方式</h4>
              <div className="space-y-4 text-gray-400">
                <div className="flex items-center gap-3 hover:text-emerald-400 transition-colors duration-300">
                  <div className="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center">
                    <Phone className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-lg">{settings.phone}</span>
                </div>
                <div className="flex items-center gap-3 hover:text-emerald-400 transition-colors duration-300">
                  <div className="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center">
                    <MapPin className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-lg">{settings.address}</span>
                </div>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p className="text-lg">&copy; {settings.copyright}</p>
          </div>
        </div>
      </footer>

      {/* 小程序码弹窗 */}
      {showQRCode && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl p-8 max-w-md w-full text-center relative">
            <button
              onClick={() => setShowQRCode(false)}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
            <div className="mb-6">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">滇护通小程序</h3>
              <p className="text-gray-600">微信扫码即可使用</p>
            </div>
            <div className="bg-gray-100 p-6 rounded-xl mb-6">
              <div className="w-48 h-48 mx-auto bg-white rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                {settings.miniprogram_qrcode ? (
                  <img
                    src={settings.miniprogram_qrcode}
                    alt="滇护通小程序码"
                    className="w-full h-full object-contain rounded-lg"
                  />
                ) : (
                  <div className="text-center">
                    <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Phone className="w-8 h-8 text-emerald-600" />
                    </div>
                    <p className="text-sm text-gray-500">小程序码</p>
                    <p className="text-xs text-gray-400 mt-1">敬请期待</p>
                  </div>
                )}
              </div>
            </div>
            <div className="space-y-2 text-sm text-gray-600">
              <p>1. 打开微信扫一扫</p>
              <p>2. 扫描上方小程序码</p>
              <p>3. 进入滇护通小程序</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
