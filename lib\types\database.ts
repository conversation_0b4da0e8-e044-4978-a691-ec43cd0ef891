// 网站设置类型
export interface SiteSetting {
  id: number
  setting_key: string
  setting_value: string
  setting_type: 'text' | 'textarea' | 'image' | 'json'
  description?: string
  created_at: Date
  updated_at: Date
}

// 合作申请类型
export interface CooperationApplication {
  id: number
  name: string
  company?: string
  phone: string
  email?: string
  region: string
  cooperation_type: string
  experience?: string
  message: string
  status: 'pending' | 'contacted' | 'completed' | 'rejected'
  admin_notes?: string
  ip_address?: string
  user_agent?: string
  created_at: Date
  updated_at: Date
}

// 图片类型
export interface Image {
  id: number
  filename: string
  original_name: string
  file_path: string
  file_size: number
  mime_type: string
  width?: number
  height?: number
  usage_type?: string
  alt_text?: string
  is_active: boolean
  uploaded_by?: string
  created_at: Date
  updated_at: Date
}

// 管理员类型
export interface Admin {
  id: number
  username: string
  password: string
  email?: string
  real_name?: string
  role: 'super_admin' | 'admin' | 'editor'
  is_active: boolean
  last_login_at?: Date
  last_login_ip?: string
  created_at: Date
  updated_at: Date
}

// 合作申请表单数据类型
export interface CooperationFormData {
  name: string
  company?: string
  phone: string
  email?: string
  region: string
  cooperation_type: string
  experience?: string
  message: string
}

// 网站设置表单数据类型
export interface SiteSettingsFormData {
  site_name: string
  hero_title: string
  hero_subtitle: string
  hero_image: string
  site_logo: string
  about_us: string
  copyright: string
  icp: string
  phone: string
  address: string
  email?: string
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 分页参数类型
export interface PaginationParams {
  page?: number
  limit?: number
  sort?: string
  order?: 'ASC' | 'DESC'
}

// 分页响应类型
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// FAQ类型
export interface FAQ {
  id: number
  question: string
  answer: string
  category: string
  sort_order: number
  is_active: boolean
  view_count: number
  created_by: string
  created_at: Date
  updated_at: Date
}

// FAQ分类类型
export interface FAQCategory {
  id: number
  category_key: string
  category_name: string
  description?: string
  sort_order: number
  is_active: boolean
  created_at: Date
  updated_at: Date
}

// FAQ表单数据类型
export interface FAQFormData {
  question: string
  answer: string
  category: string
  sort_order?: number
  is_active?: boolean
}

// 地州状态类型
export type ServiceStatus = '未开通' | '部分开通' | '已开通'

export interface PrefectureStatus {
  id: number
  prefecture_name: string
  prefecture_code: string
  service_status: ServiceStatus
  color_code: string
  description?: string
  sort_order: number
  is_active: boolean
  created_at: Date
  updated_at: Date
}

// 地州状态表单数据类型
export interface PrefectureStatusFormData {
  prefecture_name: string
  prefecture_code: string
  service_status: ServiceStatus
  color_code?: string
  description?: string
  sort_order?: number
  is_active?: boolean
}
