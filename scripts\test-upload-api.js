// 测试上传API功能的脚本
const FormData = require('form-data')
const fs = require('fs')
const path = require('path')

async function testUploadAPI() {
  console.log('🧪 开始测试上传API...')
  
  try {
    // 创建一个测试图片文件
    const testImagePath = path.join(__dirname, 'test-qrcode.png')
    
    // 如果测试图片不存在，创建一个简单的测试文件
    if (!fs.existsSync(testImagePath)) {
      console.log('📝 创建测试图片文件...')
      // 创建一个简单的PNG文件头（最小的PNG文件）
      const pngHeader = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
        0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
        0x49, 0x48, 0x44, 0x52, // IHDR
        0x00, 0x00, 0x00, 0x01, // width: 1
        0x00, 0x00, 0x00, 0x01, // height: 1
        0x08, 0x02, 0x00, 0x00, 0x00, // bit depth, color type, compression, filter, interlace
        0x90, 0x77, 0x53, 0xDE, // CRC
        0x00, 0x00, 0x00, 0x0C, // IDAT chunk length
        0x49, 0x44, 0x41, 0x54, // IDAT
        0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, // compressed data
        0x02, 0x00, 0x01, 0x00, // CRC
        0x00, 0x00, 0x00, 0x00, // IEND chunk length
        0x49, 0x45, 0x4E, 0x44, // IEND
        0xAE, 0x42, 0x60, 0x82  // CRC
      ])
      fs.writeFileSync(testImagePath, pngHeader)
      console.log('✅ 测试图片文件创建成功')
    }

    // 测试上传API
    console.log('📤 测试图片上传...')
    
    const formData = new FormData()
    formData.append('file', fs.createReadStream(testImagePath), {
      filename: 'test-qrcode.png',
      contentType: 'image/png'
    })
    formData.append('usageType', 'miniprogram_qrcode')
    formData.append('settingKey', 'miniprogram_qrcode')

    const fetch = (await import('node-fetch')).default
    
    const response = await fetch('http://localhost:3003/api/upload', {
      method: 'POST',
      body: formData,
      headers: formData.getHeaders()
    })

    const result = await response.json()
    
    console.log('📊 上传结果:')
    console.log('状态码:', response.status)
    console.log('响应:', JSON.stringify(result, null, 2))

    if (result.success) {
      console.log('✅ 上传测试成功')
      console.log('图片URL:', result.data.url)
      
      // 验证文件是否真的保存了
      const uploadedFilePath = path.join(process.cwd(), 'public', result.data.url)
      if (fs.existsSync(uploadedFilePath)) {
        console.log('✅ 文件已保存到磁盘')
      } else {
        console.log('❌ 文件未保存到磁盘')
      }
    } else {
      console.log('❌ 上传测试失败')
      console.log('错误:', result.error)
    }

    // 清理测试文件
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath)
      console.log('🧹 测试文件已清理')
    }

  } catch (error) {
    console.error('❌ 上传API测试失败:', error)
  }
}

// 测试数据库连接
async function testDatabaseConnection() {
  console.log('🔍 测试数据库连接...')
  
  try {
    const mysql = require('mysql2/promise')
    require('dotenv').config({ path: '.env.local' })
    
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME || 'dianfuto',
      port: parseInt(process.env.DB_PORT || '3306'),
    })

    console.log('✅ 数据库连接成功')
    
    // 测试查询
    const [rows] = await connection.execute('SELECT COUNT(*) as count FROM settings')
    console.log('✅ 数据库查询成功，settings表记录数:', rows[0].count)
    
    await connection.end()
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error)
  }
}

// 测试上传目录
async function testUploadDirectory() {
  console.log('📁 测试上传目录...')
  
  try {
    const uploadDir = path.join(process.cwd(), 'public/uploads')
    
    // 检查目录是否存在
    if (!fs.existsSync(uploadDir)) {
      console.log('📝 创建上传目录...')
      fs.mkdirSync(uploadDir, { recursive: true })
      console.log('✅ 上传目录创建成功')
    } else {
      console.log('✅ 上传目录已存在')
    }
    
    // 测试写入权限
    const testFile = path.join(uploadDir, 'test-write.txt')
    fs.writeFileSync(testFile, 'test')
    fs.unlinkSync(testFile)
    console.log('✅ 上传目录可写')
    
  } catch (error) {
    console.error('❌ 上传目录测试失败:', error)
  }
}

// 主测试函数
async function runUploadTests() {
  console.log('🧪 开始上传功能测试...')
  console.log('测试时间:', new Date().toLocaleString('zh-CN'))
  
  await testDatabaseConnection()
  await testUploadDirectory()
  await testUploadAPI()
  
  console.log('🎉 上传功能测试完成！')
}

// 运行测试
runUploadTests().catch(console.error)
